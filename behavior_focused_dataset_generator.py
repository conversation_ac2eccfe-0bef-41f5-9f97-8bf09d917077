"""
Behavior-Focused Dataset Generator
Maintains original three-dimensional approach: Content + Structural + Temporal
Focus: Compromised accounts targeting their existing connections
"""

import numpy as np
import pandas as pd
import torch
from datetime import datetime, timedelta
import random
import networkx as nx
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

class BehaviorFocusedDatasetGenerator:
    """
    Generate dataset focused on behavioral changes in compromised accounts
    Three dimensions: Content behavior, Structural behavior, Temporal behavior
    Core insight: Compromised accounts change their behavior when messaging existing connections
    """
    
    def __init__(self, num_users=3000, num_messages=12000, compromise_rate=0.08):
        self.num_users = num_users
        self.num_messages = num_messages
        self.compromise_rate = compromise_rate
        
        # Compromise scenarios affecting behavior patterns
        self.compromise_scenarios = {
            'account_takeover': {
                'probability': 0.4,
                'content_change': 'immediate_promotional',  # Sudden promotional content
                'structural_change': 'target_all_connections',  # Message all connections
                'temporal_change': 'burst_activity',  # Sudden activity increase
                'spam_rate': 0.4
            },
            'gradual_compromise': {
                'probability': 0.3,
                'content_change': 'slow_promotional_increase',  # Gradually more promotional
                'structural_change': 'selective_targeting',  # Target specific connections
                'temporal_change': 'pattern_shift',  # Different timing patterns
                'spam_rate': 0.25
            },
            'credential_stuffing': {
                'probability': 0.2,
                'content_change': 'obvious_spam',  # Clear spam content
                'structural_change': 'random_targeting',  # Random connection targeting
                'temporal_change': 'irregular_bursts',  # Irregular activity
                'spam_rate': 0.5
            },
            'social_engineering': {
                'probability': 0.1,
                'content_change': 'sophisticated_persuasion',  # Subtle persuasive content
                'structural_change': 'high_value_targeting',  # Target influential connections
                'temporal_change': 'normal_timing',  # Maintain normal timing
                'spam_rate': 0.15
            }
        }
        
    def generate_users_and_network(self):
        """Generate users with realistic social network"""
        users = {}
        
        # User archetypes affecting behavior patterns
        user_types = {
            'casual': {'activity_base': 5, 'connection_range': (50, 200), 'content_variety': 'high'},
            'active': {'activity_base': 12, 'connection_range': (100, 500), 'content_variety': 'medium'},
            'professional': {'activity_base': 8, 'connection_range': (200, 800), 'content_variety': 'low'},
            'influencer': {'activity_base': 20, 'connection_range': (1000, 5000), 'content_variety': 'high'},
            'business': {'activity_base': 10, 'connection_range': (300, 1500), 'content_variety': 'low'}
        }
        
        # Generate users
        for i in range(self.num_users):
            user_id = f"user_{i}"
            
            # Assign user type
            user_type = np.random.choice(
                list(user_types.keys()),
                p=[0.3, 0.25, 0.2, 0.05, 0.2]
            )
            
            type_config = user_types[user_type]
            
            # User characteristics
            age = np.random.randint(18, 70)
            account_age_days = np.random.randint(30, 2190)
            
            # Activity patterns
            base_activity = type_config['activity_base']
            weekly_pattern = []
            for day in range(7):
                if day < 5:  # Weekdays
                    day_activity = int(base_activity * np.random.uniform(0.8, 1.3))
                else:  # Weekends
                    day_activity = int(base_activity * np.random.uniform(0.5, 1.5))
                weekly_pattern.append(max(0, day_activity))
            
            # Connection characteristics
            connection_range = type_config['connection_range']
            num_connections = np.random.randint(*connection_range)
            
            # Vulnerability calculation (realistic factors)
            vulnerability = 0.0
            
            # Age factor
            if age > 55:
                vulnerability += 0.2
            elif age < 25:
                vulnerability += 0.15
            
            # Account age factor
            if account_age_days < 180:
                vulnerability += 0.15
            
            # User type factor (influencers/business more targeted)
            if user_type in ['influencer', 'business']:
                vulnerability += 0.1
            
            # Activity factor (very high activity = more exposure)
            if base_activity > 15:
                vulnerability += 0.1
            
            # Random factor
            vulnerability += np.random.uniform(-0.1, 0.1)
            vulnerability = np.clip(vulnerability, 0, 1)
            
            # Determine compromise
            is_compromised = vulnerability > (1 - self.compromise_rate * 2)
            
            # Compromise scenario
            compromise_scenario = None
            compromise_date = None
            if is_compromised:
                compromise_scenario = np.random.choice(
                    list(self.compromise_scenarios.keys()),
                    p=[0.4, 0.3, 0.2, 0.1]
                )
                compromise_date = datetime.now() - timedelta(days=np.random.randint(1, 90))
            
            users[user_id] = {
                'user_id': user_id,
                'user_type': user_type,
                'age': age,
                'account_age_days': account_age_days,
                'base_activity': base_activity,
                'weekly_pattern': weekly_pattern,
                'num_connections': num_connections,
                'content_variety': type_config['content_variety'],
                'vulnerability': vulnerability,
                'is_compromised': is_compromised,
                'compromise_scenario': compromise_scenario,
                'compromise_date': compromise_date,
                'connections': set(),
                'followers': set(),
                'following': set()
            }
        
        # Generate social network
        network_graph = self._generate_social_network(users)
        
        return users, network_graph
    
    def _generate_social_network(self, users):
        """Generate realistic social network connections"""
        G = nx.Graph()
        user_list = list(users.keys())
        G.add_nodes_from(user_list)
        
        for user_id, user_data in users.items():
            num_connections = user_data['num_connections']
            user_type = user_data['user_type']
            age = user_data['age']
            
            # Select connections based on homophily
            potential_connections = []
            
            for other_id, other_data in users.items():
                if other_id == user_id:
                    continue
                
                # Calculate connection probability
                age_similarity = 1 - abs(age - other_data['age']) / 50
                type_compatibility = 1 if user_type == other_data['user_type'] else 0.3
                
                # Influencers and businesses attract more connections
                if other_data['user_type'] in ['influencer', 'business']:
                    attraction_bonus = 0.3
                else:
                    attraction_bonus = 0
                
                connection_prob = (age_similarity + type_compatibility + attraction_bonus) / 2
                potential_connections.append((other_id, connection_prob))
            
            # Sort by probability and select connections
            potential_connections.sort(key=lambda x: x[1] + np.random.uniform(0, 0.2), reverse=True)
            
            actual_connections = min(num_connections, len(potential_connections))
            selected_connections = potential_connections[:actual_connections]
            
            # Add edges
            for connected_user, prob in selected_connections:
                if not G.has_edge(user_id, connected_user):
                    weight = int(prob * 10) + np.random.randint(1, 5)
                    G.add_edge(user_id, connected_user, weight=weight)
                    
                    # Update user connection sets
                    users[user_id]['connections'].add(connected_user)
                    users[connected_user]['connections'].add(user_id)
                    
                    # For directed relationships (following)
                    if np.random.random() < prob:
                        users[user_id]['following'].add(connected_user)
                        users[connected_user]['followers'].add(user_id)
        
        return G
    
    def generate_behavioral_messages(self, users, network_graph):
        """Generate messages showing behavioral changes in compromised accounts"""
        messages = []
        
        for i in range(self.num_messages):
            # Select sender based on activity level
            sender_weights = []
            for user_id, user_data in users.items():
                base_weight = sum(user_data['weekly_pattern'])
                
                # Compromised users are more active
                if user_data['is_compromised']:
                    scenario = self.compromise_scenarios[user_data['compromise_scenario']]
                    if scenario['temporal_change'] == 'burst_activity':
                        base_weight *= 2
                    elif scenario['temporal_change'] == 'irregular_bursts':
                        base_weight *= 1.5
                
                sender_weights.append(base_weight)
            
            if sum(sender_weights) == 0:
                continue
            
            sender_weights = np.array(sender_weights) / sum(sender_weights)
            sender_idx = np.random.choice(len(users), p=sender_weights)
            sender_id = f"user_{sender_idx}"
            sender = users[sender_id]
            
            # Select recipient from existing connections
            if not sender['connections']:
                continue
            
            recipient_id = self._select_recipient_based_on_behavior(sender, users)
            if not recipient_id:
                continue
            
            # Determine if spam based on behavioral patterns
            is_spam = self._determine_spam_from_behavior(sender)
            
            # Generate content based on behavioral changes
            content = self._generate_behavioral_content(sender, is_spam, recipient_id)
            
            # Generate timestamp based on temporal behavioral changes
            timestamp = self._generate_behavioral_timestamp(sender, is_spam)
            
            message = {
                'message_id': f"msg_{i}",
                'sender_id': sender_id,
                'recipient_id': recipient_id,
                'content': content,
                'timestamp': timestamp,
                'is_spam': is_spam,
                'sender_compromised': sender['is_compromised'],
                'compromise_scenario': sender['compromise_scenario']
            }
            
            messages.append(message)
        
        return messages
    
    def _select_recipient_based_on_behavior(self, sender, users):
        """Select recipient based on compromise behavioral patterns"""
        if not sender['connections']:
            return None
        
        if not sender['is_compromised']:
            # Normal users: random selection from connections
            return np.random.choice(list(sender['connections']))
        
        # Compromised users: behavioral targeting
        scenario = self.compromise_scenarios[sender['compromise_scenario']]
        structural_change = scenario['structural_change']
        
        if structural_change == 'target_all_connections':
            # Target all connections equally
            return np.random.choice(list(sender['connections']))
        
        elif structural_change == 'selective_targeting':
            # Target specific types of connections
            # Prefer active users or influencers
            preferred_targets = []
            for conn_id in sender['connections']:
                conn_user = users[conn_id]
                if conn_user['user_type'] in ['active', 'influencer'] or conn_user['base_activity'] > 10:
                    preferred_targets.append(conn_id)
            
            if preferred_targets:
                return np.random.choice(preferred_targets)
            else:
                return np.random.choice(list(sender['connections']))
        
        elif structural_change == 'high_value_targeting':
            # Target influencers and business accounts
            high_value_targets = []
            for conn_id in sender['connections']:
                conn_user = users[conn_id]
                if conn_user['user_type'] in ['influencer', 'business']:
                    high_value_targets.append(conn_id)
            
            if high_value_targets:
                return np.random.choice(high_value_targets)
            else:
                return np.random.choice(list(sender['connections']))
        
        else:  # random_targeting
            return np.random.choice(list(sender['connections']))
    
    def _determine_spam_from_behavior(self, sender):
        """Determine spam based on behavioral patterns"""
        if not sender['is_compromised']:
            # Normal users: very low spam rate
            if sender['user_type'] in ['business', 'influencer']:
                return np.random.random() < 0.05  # 5% promotional content
            else:
                return np.random.random() < 0.01  # 1% false positives
        
        # Compromised users: scenario-based spam rates
        scenario = self.compromise_scenarios[sender['compromise_scenario']]
        spam_rate = scenario['spam_rate']
        
        # Temporal factor: more spam right after compromise
        if sender['compromise_date']:
            days_since_compromise = (datetime.now() - sender['compromise_date']).days
            if days_since_compromise < 7:  # First week
                spam_rate *= 1.5
            elif days_since_compromise < 30:  # First month
                spam_rate *= 1.2
        
        return np.random.random() < spam_rate
    
    def _generate_behavioral_content(self, sender, is_spam, recipient_id):
        """Generate content based on behavioral changes"""
        if not is_spam:
            # Normal content
            normal_templates = [
                f"Hey {recipient_id}, how are you doing?",
                f"Thanks for connecting!",
                f"Hope you're having a great day!",
                f"Saw your post, really interesting!",
                f"Let's catch up soon!"
            ]
            return np.random.choice(normal_templates)
        
        # Spam content based on compromise behavior
        if not sender['is_compromised']:
            # False positive spam (business/influencer promotional)
            promo_templates = [
                f"Hi {recipient_id}, check out our new product!",
                f"Special offer for my connections!",
                f"Thought you might be interested in this."
            ]
            return np.random.choice(promo_templates)
        
        # Compromised account spam
        scenario = self.compromise_scenarios[sender['compromise_scenario']]
        content_change = scenario['content_change']
        
        if content_change == 'immediate_promotional':
            templates = [
                f"Hey {recipient_id}! Amazing opportunity - check this out!",
                f"You won't believe this deal I found!",
                f"Limited time offer - don't miss out!",
                f"This changed my life, you should try it!"
            ]
        elif content_change == 'slow_promotional_increase':
            templates = [
                f"Hi {recipient_id}, found something interesting to share.",
                f"Thought you might like this opportunity.",
                f"This might be helpful for you.",
                f"Check this out when you have time."
            ]
        elif content_change == 'obvious_spam':
            templates = [
                f"URGENT! Make money fast! Click here now!",
                f"You've won $1000! Claim immediately!",
                f"Work from home! Earn $500/day!",
                f"Free gift! Limited time! Act now!"
            ]
        elif content_change == 'sophisticated_persuasion':
            templates = [
                f"Hi {recipient_id}, I've been using this service and thought you'd appreciate it.",
                f"Found a solution that might help with your recent post.",
                f"This opportunity aligns with your interests.",
                f"Thought this might be valuable for your work."
            ]
        else:
            templates = [
                f"Hey {recipient_id}, check this out!",
                f"Interesting opportunity to share.",
                f"You might find this useful."
            ]
        
        return np.random.choice(templates)
    
    def _generate_behavioral_timestamp(self, sender, is_spam):
        """Generate timestamp based on temporal behavioral changes"""
        base_time = datetime.now() - timedelta(hours=np.random.randint(0, 168))
        
        if not sender['is_compromised'] or not is_spam:
            return base_time
        
        # Compromised account temporal behavior
        scenario = self.compromise_scenarios[sender['compromise_scenario']]
        temporal_change = scenario['temporal_change']
        
        if temporal_change == 'burst_activity':
            # Cluster messages in short time periods
            if np.random.random() < 0.6:  # 60% chance of burst
                burst_offset = np.random.randint(-2, 2)  # Within 2 hours
                base_time += timedelta(hours=burst_offset)
        
        elif temporal_change == 'pattern_shift':
            # Different time patterns after compromise
            if sender['compromise_date']:
                days_since_compromise = (datetime.now() - sender['compromise_date']).days
                if days_since_compromise < 30:  # First month after compromise
                    # Shift to unusual hours
                    hour_shift = np.random.randint(3, 8)  # 3-8 hour shift
                    base_time += timedelta(hours=hour_shift)
        
        elif temporal_change == 'irregular_bursts':
            # Random irregular activity
            if np.random.random() < 0.4:  # 40% chance of irregular timing
                irregular_offset = np.random.randint(-12, 12)
                base_time += timedelta(hours=irregular_offset)
        
        return base_time
    
    def generate_complete_dataset(self):
        """Generate complete dataset focused on behavioral changes"""
        print(f"🔬 Generating behavior-focused dataset with {self.num_users} users...")
        print("🎯 Focus: Behavioral changes in compromised accounts (Content + Structural + Temporal)")
        
        # Generate users and network
        users, network_graph = self.generate_users_and_network()
        compromised_count = sum(1 for u in users.values() if u['is_compromised'])
        
        print(f"  📊 Generated {len(users)} users ({compromised_count} compromised, {compromised_count/len(users)*100:.1f}%)")
        print(f"  🌐 Generated network with {network_graph.number_of_edges()} connections")
        
        # Generate behavioral messages
        messages = self.generate_behavioral_messages(users, network_graph)
        spam_count = sum(1 for m in messages if m['is_spam'])
        
        print(f"  💬 Generated {len(messages)} messages ({spam_count} spam, {spam_count/len(messages)*100:.1f}%)")
        
        # Calculate compromise scenario distribution
        scenario_dist = {}
        for user in users.values():
            if user['compromise_scenario']:
                scenario_dist[user['compromise_scenario']] = scenario_dist.get(user['compromise_scenario'], 0) + 1
        
        print(f"  🎯 Compromise scenarios: {scenario_dist}")
        
        return {
            'users': users,
            'messages': messages,
            'network_graph': network_graph,
            'statistics': {
                'num_users': len(users),
                'num_compromised': compromised_count,
                'compromise_rate': compromised_count / len(users),
                'num_messages': len(messages),
                'num_spam': spam_count,
                'spam_rate': spam_count / len(messages),
                'num_connections': network_graph.number_of_edges(),
                'compromise_scenarios': scenario_dist
            }
        }

# Usage example
if __name__ == "__main__":
    generator = BehaviorFocusedDatasetGenerator(num_users=2000, num_messages=8000)
    dataset = generator.generate_complete_dataset()
    
    print("\\n🎯 Example Behavioral Messages:")
    behavioral_messages = [msg for msg in dataset['messages'] if msg['sender_compromised']]
    for i, msg in enumerate(behavioral_messages[:3]):
        print(f"  {i+1}. {msg['sender_id']} → {msg['recipient_id']}")
        print(f"     Content: {msg['content']}")
        print(f"     Scenario: {msg['compromise_scenario']}")
        print(f"     Spam: {msg['is_spam']}")
