"""
Realistic Dataset Generator for Compromised Account Detection
Addresses reviewer concerns about oversimplified synthetic data
"""

import numpy as np
import pandas as pd
import torch
from datetime import datetime, timedelta
import random
from transformers import DistilBertTokenizer, DistilBertModel
import networkx as nx
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class RealisticDatasetGenerator:
    """
    Generate realistic social network data that prevents data leakage
    and creates appropriate challenge level for advanced methods
    """
    
    def __init__(self, num_users=3000, num_messages=12000, compromise_rate=0.08):
        self.num_users = num_users
        self.num_messages = num_messages
        self.compromise_rate = compromise_rate
        
        # Initialize BERT for content embeddings
        self.tokenizer = DistilBertTokenizer.from_pretrained('distilbert-base-uncased')
        self.bert_model = DistilBertModel.from_pretrained('distilbert-base-uncased')
        self.bert_model.eval()
        
        # User behavior profiles
        self.user_profiles = {
            'casual': {'activity_mean': 3, 'activity_std': 2, 'followers_range': (50, 300)},
            'active': {'activity_mean': 12, 'activity_std': 4, 'followers_range': (200, 1000)},
            'influencer': {'activity_mean': 25, 'activity_std': 8, 'followers_range': (1000, 10000)},
            'business': {'activity_mean': 8, 'activity_std': 3, 'followers_range': (300, 3000)},
            'lurker': {'activity_mean': 1, 'activity_std': 1, 'followers_range': (20, 150)}
        }
        
        # Realistic message templates
        self.legitimate_templates = [
            \"Just had an amazing coffee at {location}! ☕\",\n            \"Working on {project} today. Excited about the progress! 💪\",\n            \"Beautiful sunset tonight. Nature never fails to amaze me 🌅\",\n            \"Thanks everyone for the birthday wishes! Feeling grateful 🎂\",\n            \"Finished reading {book}. Highly recommend it! 📚\",\n            \"Weekend plans: {activity}. Can't wait! 🎉\",\n            \"Proud of my team for {achievement}. Great work everyone! 👏\",\n            \"Trying out {restaurant} for dinner. The {dish} looks incredible! 🍽️\"\n        ]\n        \n        # Subtle spam patterns (harder to detect)\n        self.subtle_spam_templates = [\n            \"Hey! I found this amazing {product} that changed my life. Check it out: {url}\",\n            \"Just earned ${amount} this week with this simple method. DM me for details!\",\n            \"Limited spots available for {course}. Don't miss out! Link in bio.\",\n            \"Free {item} for the first 100 people. Claim yours now: {url}\",\n            \"This {product} helped me {benefit}. Use code SAVE20 for discount!\",\n            \"Work from home opportunity. Flexible hours, great pay. Message me!\",\n            \"Investment tip: {stock} is about to explode! Get in now before it's too late.\",\n            \"Lost 20 pounds with {product}! Amazing results. Try it yourself: {url}\"\n        ]\n        \n    def generate_realistic_users(self):\n        \"\"\"Generate users with realistic behavioral patterns\"\"\"\n        users = {}\n        \n        for i in range(self.num_users):\n            user_id = f\"user_{i}\"\n            \n            # Assign user type with realistic distribution\n            user_type = np.random.choice(\n                ['casual', 'active', 'influencer', 'business', 'lurker'],\n                p=[0.4, 0.3, 0.05, 0.15, 0.1]  # Realistic distribution\n            )\n            \n            profile = self.user_profiles[user_type]\n            \n            # Compromise probability based on user type (influencers more targeted)\n            base_compromise_prob = self.compromise_rate\n            if user_type == 'influencer':\n                compromise_prob = base_compromise_prob * 2.5\n            elif user_type == 'business':\n                compromise_prob = base_compromise_prob * 1.8\n            elif user_type == 'active':\n                compromise_prob = base_compromise_prob * 1.2\n            else:\n                compromise_prob = base_compromise_prob * 0.8\n                \n            is_compromised = np.random.random() < compromise_prob\n            \n            # Generate realistic follower counts\n            follower_range = profile['followers_range']\n            follower_count = np.random.randint(*follower_range)\n            following_count = int(follower_count * np.random.uniform(0.1, 0.6))\n            \n            # Account age affects behavior\n            account_age_days = np.random.randint(30, 1095)  # 1 month to 3 years\n            \n            # Generate weekly activity pattern\n            base_activity = max(1, int(np.random.normal(profile['activity_mean'], profile['activity_std'])))\n            weekly_pattern = []\n            for day in range(7):\n                if day < 5:  # Weekdays\n                    day_activity = int(base_activity * np.random.uniform(0.8, 1.2))\n                else:  # Weekends\n                    day_activity = int(base_activity * np.random.uniform(0.6, 1.4))\n                weekly_pattern.append(max(0, day_activity))\n            \n            # If compromised, introduce subtle behavioral changes\n            if is_compromised:\n                # Compromise happened recently\n                compromise_date = datetime.now() - timedelta(days=np.random.randint(1, 60))\n                \n                # Subtle changes in activity pattern\n                weekly_pattern = [max(0, int(x * np.random.uniform(0.7, 1.5))) for x in weekly_pattern]\n                \n                # Unusual timing patterns\n                night_activity_increase = np.random.uniform(1.2, 2.0)\n                posting_time_variance = np.random.uniform(1.5, 3.0)\n            else:\n                compromise_date = None\n                night_activity_increase = 1.0\n                posting_time_variance = 1.0\n            \n            users[user_id] = {\n                'user_id': user_id,\n                'user_type': user_type,\n                'is_compromised': is_compromised,\n                'follower_count': follower_count,\n                'following_count': following_count,\n                'account_age_days': account_age_days,\n                'weekly_activity_pattern': weekly_pattern,\n                'compromise_date': compromise_date,\n                'night_activity_multiplier': night_activity_increase,\n                'posting_time_variance': posting_time_variance\n            }\n        \n        return users\n    \n    def generate_realistic_messages(self, users):\n        \"\"\"Generate messages with realistic content and timing\"\"\"\n        messages = []\n        \n        # Create realistic vocabulary for substitutions\n        locations = ['Central Park', 'downtown', 'the mall', 'home', 'work', 'the gym']\n        projects = ['my thesis', 'the new app', 'this presentation', 'the website', 'our startup']\n        books = ['The Great Gatsby', 'Sapiens', '1984', 'The Alchemist', 'Atomic Habits']\n        activities = ['hiking', 'movie night', 'beach trip', 'family dinner', 'concert']\n        restaurants = ['that new Italian place', 'the sushi bar', 'the local diner', 'the food truck']\n        dishes = ['pasta', 'ramen', 'burger', 'tacos', 'pizza']\n        \n        # Spam-related vocabulary\n        products = ['supplement', 'course', 'ebook', 'software', 'system']\n        benefits = ['lose weight', 'make money', 'save time', 'get fit', 'learn faster']\n        stocks = ['AAPL', 'TSLA', 'AMZN', 'GOOGL', 'MSFT']\n        \n        for i in range(self.num_messages):\n            # Select random user\n            user_id = f\"user_{np.random.randint(0, self.num_users)}\"\n            user = users[user_id]\n            \n            # Determine if this message should be spam\n            if user['is_compromised']:\n                # Compromised users post spam with some probability, but not always\n                is_spam = np.random.random() < 0.25  # Only 25% of compromised user messages are spam\n            else:\n                # Legitimate users very rarely post spam-like content\n                is_spam = np.random.random() < 0.02  # 2% false positive rate\n            \n            # Generate message content\n            if is_spam:\n                template = np.random.choice(self.subtle_spam_templates)\n                content = template.format(\n                    product=np.random.choice(products),\n                    amount=np.random.randint(100, 2000),\n                    course=f\"{np.random.choice(['Marketing', 'Coding', 'Trading', 'Fitness'])} Masterclass\",\n                    item=np.random.choice(['trial', 'sample', 'guide', 'consultation']),\n                    benefit=np.random.choice(benefits),\n                    stock=np.random.choice(stocks),\n                    url=\"bit.ly/\" + ''.join(np.random.choice(list('abcdefghijklmnopqrstuvwxyz0123456789'), 6))\n                )\n            else:\n                template = np.random.choice(self.legitimate_templates)\n                content = template.format(\n                    location=np.random.choice(locations),\n                    project=np.random.choice(projects),\n                    book=np.random.choice(books),\n                    activity=np.random.choice(activities),\n                    restaurant=np.random.choice(restaurants),\n                    dish=np.random.choice(dishes),\n                    achievement=np.random.choice(['completing the project', 'hitting our targets', 'the successful launch'])\n                )\n            \n            # Generate realistic timestamp\n            base_time = datetime.now() - timedelta(hours=np.random.randint(0, 168))  # Last week\n            \n            # Apply user-specific timing patterns\n            if user['is_compromised'] and is_spam:\n                # Compromised accounts might post at unusual times\n                hour_offset = int(np.random.normal(0, user['posting_time_variance'] * 3))\n                base_time += timedelta(hours=hour_offset)\n            \n            messages.append({\n                'message_id': f\"msg_{i}\",\n                'user_id': user_id,\n                'content': content,\n                'timestamp': base_time,\n                'is_spam': is_spam,\n                'is_user_compromised': user['is_compromised']\n            })\n        \n        return messages\n    \n    def generate_realistic_network(self, users):\n        \"\"\"Generate realistic social network connections\"\"\"\n        interactions = []\n        user_list = list(users.keys())\n        \n        # Create realistic network structure\n        G = nx.Graph()\n        G.add_nodes_from(user_list)\n        \n        # Add edges based on user types and follower counts\n        for user_id, user_data in users.items():\n            user_type = user_data['user_type']\n            follower_count = user_data['follower_count']\n            \n            # Determine number of connections based on user type\n            if user_type == 'influencer':\n                num_connections = min(len(user_list) - 1, int(follower_count * 0.1))\n            elif user_type == 'business':\n                num_connections = min(len(user_list) - 1, int(follower_count * 0.05))\n            elif user_type == 'active':\n                num_connections = min(len(user_list) - 1, int(follower_count * 0.15))\n            else:\n                num_connections = min(len(user_list) - 1, int(follower_count * 0.2))\n            \n            # Select connections with preference for similar user types\n            potential_connections = [uid for uid in user_list if uid != user_id]\n            \n            # Bias towards connecting with similar user types\n            same_type_users = [uid for uid in potential_connections \n                             if users[uid]['user_type'] == user_type]\n            \n            if same_type_users and len(same_type_users) > num_connections // 2:\n                selected_same_type = np.random.choice(\n                    same_type_users, \n                    size=min(num_connections // 2, len(same_type_users)), \n                    replace=False\n                )\n                remaining_connections = num_connections - len(selected_same_type)\n                other_users = [uid for uid in potential_connections if uid not in selected_same_type]\n                \n                if other_users and remaining_connections > 0:\n                    selected_others = np.random.choice(\n                        other_users,\n                        size=min(remaining_connections, len(other_users)),\n                        replace=False\n                    )\n                    connections = list(selected_same_type) + list(selected_others)\n                else:\n                    connections = list(selected_same_type)\n            else:\n                connections = np.random.choice(\n                    potential_connections,\n                    size=min(num_connections, len(potential_connections)),\n                    replace=False\n                )\n            \n            # Add edges with weights\n            for connected_user in connections:\n                if not G.has_edge(user_id, connected_user):\n                    # Weight based on interaction frequency\n                    weight = np.random.randint(1, 10)\n                    G.add_edge(user_id, connected_user, weight=weight)\n                    interactions.append((user_id, connected_user, weight))\n        \n        return interactions, G\n    \n    def generate_complete_dataset(self):\n        \"\"\"Generate complete realistic dataset\"\"\"\n        print(f\"🔬 Generating realistic dataset with {self.num_users} users...\")\n        \n        # Generate users\n        users = self.generate_realistic_users()\n        compromised_count = sum(1 for u in users.values() if u['is_compromised'])\n        print(f\"  📊 Generated {len(users)} users ({compromised_count} compromised, {compromised_count/len(users)*100:.1f}%)\")\n        \n        # Generate messages\n        messages = self.generate_realistic_messages(users)\n        spam_count = sum(1 for m in messages if m['is_spam'])\n        print(f\"  💬 Generated {len(messages)} messages ({spam_count} spam, {spam_count/len(messages)*100:.1f}%)\")\n        \n        # Generate network\n        interactions, network_graph = self.generate_realistic_network(users)\n        print(f\"  🌐 Generated network with {len(interactions)} connections\")\n        \n        return {\n            'users': users,\n            'messages': messages,\n            'interactions': interactions,\n            'network_graph': network_graph,\n            'statistics': {\n                'num_users': len(users),\n                'num_compromised': compromised_count,\n                'compromise_rate': compromised_count / len(users),\n                'num_messages': len(messages),\n                'num_spam': spam_count,\n                'spam_rate': spam_count / len(messages),\n                'num_connections': len(interactions),\n                'network_density': len(interactions) / (len(users) * (len(users) - 1) / 2)\n            }\n        }\n\n# Usage example\nif __name__ == \"__main__\":\n    generator = RealisticDatasetGenerator(num_users=2000, num_messages=8000)\n    dataset = generator.generate_complete_dataset()\n    print(\"\\n📈 Dataset Statistics:\")\n    for key, value in dataset['statistics'].items():\n        print(f\"  {key}: {value}\")
