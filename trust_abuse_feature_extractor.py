"""
Trust Abuse Feature Extractor
Focused feature extraction for detecting compromised accounts that spam trusted connections
"""

import numpy as np
import pandas as pd
import torch
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import networkx as nx
from sklearn.preprocessing import StandardScaler
from transformers import DistilBertTokenizer, DistilBertModel
import warnings
warnings.filterwarnings('ignore')

class TrustAbuseFeatureExtractor:
    """
    Extract features specifically designed to detect trust relationship abuse
    Core insight: Compromised accounts change their messaging patterns to trusted connections
    """
    
    def __init__(self):
        # Initialize BERT for content analysis
        self.tokenizer = DistilBertTokenizer.from_pretrained('distilbert-base-uncased')
        self.bert_model = DistilBertModel.from_pretrained('distilbert-base-uncased')
        self.bert_model.eval()
        
        # Trust abuse indicators
        self.spam_keywords = [
            'opportunity', 'deal', 'offer', 'limited time', 'act now', 'click here',\n            'make money', 'earn', 'free', 'discount', 'save', 'buy now', 'order',\n            'investment', 'profit', 'income', 'business opportunity', 'work from home'\n        ]\n        \n        self.trust_exploitation_phrases = [\n            'i found', 'you should try', 'i recommend', 'trust me', 'believe me',\n            'i had to share', 'you need this', 'this worked for me', 'helped me'\n        ]\n    \n    def extract_comprehensive_features(self, dataset):\n        \"\"\"Extract all features for trust abuse detection\"\"\"\n        users = dataset['users']\n        messages = dataset['messages']\n        trust_graph = dataset['trust_graph']\n        \n        print(\"🔍 Extracting trust abuse detection features...\")\n        \n        # Extract features for each user\n        user_features = {}\n        \n        for user_id, user_data in users.items():\n            print(f\"  Processing {user_id}...\", end='\\r')\n            \n            # Get user's messages\n            user_messages = [msg for msg in messages if msg['sender_id'] == user_id]\n            \n            # Extract multi-dimensional features\n            structural_features = self._extract_structural_features(user_id, user_data, trust_graph)\n            temporal_features = self._extract_temporal_features(user_id, user_messages, user_data)\n            content_features = self._extract_content_features(user_messages)\n            trust_features = self._extract_trust_abuse_features(user_id, user_messages, users)\n            behavioral_features = self._extract_behavioral_change_features(user_id, user_messages, user_data)\n            \n            # Combine all features\n            user_features[user_id] = {\n                'structural': structural_features,\n                'temporal': temporal_features,\n                'content': content_features,\n                'trust_abuse': trust_features,\n                'behavioral_change': behavioral_features,\n                'label': user_data['is_compromised']  # Ground truth\n            }\n        \n        print(\"\\n✅ Feature extraction completed!\")\n        return user_features\n    \n    def _extract_structural_features(self, user_id, user_data, trust_graph):\n        \"\"\"Extract network structural features focused on trust relationships\"\"\"\n        features = {}\n        \n        # Basic network metrics\n        if user_id in trust_graph:\n            # Trust network centrality\n            features['trust_degree_centrality'] = trust_graph.degree(user_id) / (len(trust_graph.nodes()) - 1)\n            \n            # Trust relationship distribution\n            trust_levels = [trust_graph[user_id][neighbor]['trust_level'] \n                          for neighbor in trust_graph.neighbors(user_id)]\n            trust_counter = Counter(trust_levels)\n            \n            features['close_friend_ratio'] = trust_counter.get('close_friend', 0) / max(len(trust_levels), 1)\n            features['friend_ratio'] = trust_counter.get('friend', 0) / max(len(trust_levels), 1)\n            features['acquaintance_ratio'] = trust_counter.get('acquaintance', 0) / max(len(trust_levels), 1)\n            \n            # Trust diversity (how varied are the trust relationships)\n            features['trust_diversity'] = len(set(trust_levels)) / 5  # 5 possible trust levels\n            \n            # Average trust level (numerical)\n            trust_values = {'close_friend': 5, 'friend': 4, 'acquaintance': 3, 'follower': 2, 'stranger': 1}\n            avg_trust = np.mean([trust_values[tl] for tl in trust_levels]) if trust_levels else 0\n            features['average_trust_level'] = avg_trust / 5  # Normalize\n        else:\n            # No connections\n            features.update({\n                'trust_degree_centrality': 0,\n                'close_friend_ratio': 0,\n                'friend_ratio': 0,\n                'acquaintance_ratio': 0,\n                'trust_diversity': 0,\n                'average_trust_level': 0\n            })\n        \n        # User characteristics that affect trust\n        features['trustworthiness_score'] = user_data.get('trustworthiness_score', 0.5)\n        features['social_influence'] = user_data.get('social_influence', 0.1)\n        features['account_age_normalized'] = min(user_data['account_age_days'] / 2190, 1.0)  # Normalize to 6 years\n        \n        return features\n    \n    def _extract_temporal_features(self, user_id, user_messages, user_data):\n        \"\"\"Extract temporal features focused on compromise detection\"\"\"\n        features = {}\n        \n        if not user_messages:\n            return {f'temporal_{i}': 0 for i in range(15)}  # Return zeros\n        \n        # Message timing analysis\n        timestamps = [msg['timestamp'] for msg in user_messages]\n        timestamps.sort()\n        \n        # Activity patterns\n        features['total_messages'] = len(user_messages)\n        features['messages_per_day'] = len(user_messages) / max((timestamps[-1] - timestamps[0]).days, 1)\n        \n        # Time-of-day analysis\n        hours = [ts.hour for ts in timestamps]\n        features['night_activity_ratio'] = sum(1 for h in hours if h < 6 or h > 22) / len(hours)\n        features['business_hours_ratio'] = sum(1 for h in hours if 9 <= h <= 17) / len(hours)\n        \n        # Activity variance (compromised accounts might have irregular patterns)\n        daily_counts = defaultdict(int)\n        for ts in timestamps:\n            daily_counts[ts.date()] += 1\n        \n        daily_activity = list(daily_counts.values())\n        features['activity_variance'] = np.var(daily_activity) if len(daily_activity) > 1 else 0\n        features['activity_mean'] = np.mean(daily_activity)\n        \n        # Compromise-specific temporal patterns\n        if user_data.get('compromise_date'):\n            compromise_date = user_data['compromise_date']\n            \n            # Messages before vs after compromise\n            pre_compromise = [msg for msg in user_messages if msg['timestamp'] < compromise_date]\n            post_compromise = [msg for msg in user_messages if msg['timestamp'] >= compromise_date]\n            \n            features['pre_compromise_messages'] = len(pre_compromise)\n            features['post_compromise_messages'] = len(post_compromise)\n            \n            # Activity change after compromise\n            if len(pre_compromise) > 0 and len(post_compromise) > 0:\n                pre_rate = len(pre_compromise) / max((compromise_date - timestamps[0]).days, 1)\n                post_rate = len(post_compromise) / max((timestamps[-1] - compromise_date).days, 1)\n                features['activity_change_ratio'] = post_rate / max(pre_rate, 0.1)\n            else:\n                features['activity_change_ratio'] = 1.0\n        else:\n            features.update({\n                'pre_compromise_messages': len(user_messages),\n                'post_compromise_messages': 0,\n                'activity_change_ratio': 1.0\n            })\n        \n        # Burst detection (sudden increases in activity)\n        if len(timestamps) > 5:\n            time_diffs = [(timestamps[i+1] - timestamps[i]).total_seconds() / 3600 for i in range(len(timestamps)-1)]\n            features['min_time_between_messages'] = min(time_diffs)\n            features['avg_time_between_messages'] = np.mean(time_diffs)\n            features['burst_activity_ratio'] = sum(1 for td in time_diffs if td < 1) / len(time_diffs)  # Messages within 1 hour\n        else:\n            features.update({\n                'min_time_between_messages': 24,\n                'avg_time_between_messages': 24,\n                'burst_activity_ratio': 0\n            })\n        \n        return features\n    \n    def _extract_content_features(self, user_messages):\n        \"\"\"Extract content features using BERT and keyword analysis\"\"\"\n        features = {}\n        \n        if not user_messages:\n            return {f'content_{i}': 0 for i in range(20)}  # Return zeros\n        \n        # Separate spam and non-spam messages\n        spam_messages = [msg for msg in user_messages if msg['is_spam']]\n        non_spam_messages = [msg for msg in user_messages if not msg['is_spam']]\n        \n        features['spam_ratio'] = len(spam_messages) / len(user_messages)\n        \n        # Content analysis for all messages\n        all_content = [msg['content'] for msg in user_messages]\n        \n        # Keyword analysis\n        spam_keyword_count = 0\n        trust_exploitation_count = 0\n        \n        for content in all_content:\n            content_lower = content.lower()\n            spam_keyword_count += sum(1 for keyword in self.spam_keywords if keyword in content_lower)\n            trust_exploitation_count += sum(1 for phrase in self.trust_exploitation_phrases if phrase in content_lower)\n        \n        features['spam_keywords_per_message'] = spam_keyword_count / len(all_content)\n        features['trust_exploitation_per_message'] = trust_exploitation_count / len(all_content)\n        \n        # Content diversity\n        unique_words = set()\n        total_words = 0\n        for content in all_content:\n            words = content.lower().split()\n            unique_words.update(words)\n            total_words += len(words)\n        \n        features['vocabulary_diversity'] = len(unique_words) / max(total_words, 1)\n        features['avg_message_length'] = total_words / len(all_content)\n        \n        # BERT embeddings for semantic analysis\n        if len(all_content) > 0:\n            # Sample up to 10 messages for BERT analysis (computational efficiency)\n            sample_content = all_content[:10] if len(all_content) > 10 else all_content\n            \n            embeddings = []\n            for content in sample_content:\n                inputs = self.tokenizer(content, return_tensors='pt', truncation=True, max_length=128)\n                with torch.no_grad():\n                    outputs = self.bert_model(**inputs)\n                    # Use [CLS] token embedding\n                    embedding = outputs.last_hidden_state[0, 0, :].numpy()\n                    embeddings.append(embedding)\n            \n            # Average BERT embedding\n            avg_embedding = np.mean(embeddings, axis=0)\n            \n            # Use first 10 dimensions of BERT embedding as features\n            for i in range(10):\n                features[f'bert_dim_{i}'] = avg_embedding[i]\n        else:\n            # No content available\n            for i in range(10):\n                features[f'bert_dim_{i}'] = 0\n        \n        return features\n    \n    def _extract_trust_abuse_features(self, user_id, user_messages, users):\n        \"\"\"Extract features specifically for trust abuse detection\"\"\"\n        features = {}\n        \n        if not user_messages:\n            return {f'trust_abuse_{i}': 0 for i in range(10)}  # Return zeros\n        \n        # Trust-specific messaging patterns\n        trust_level_spam = defaultdict(list)\n        trust_level_total = defaultdict(int)\n        \n        for msg in user_messages:\n            trust_level = msg.get('trust_level', 'unknown')\n            trust_level_total[trust_level] += 1\n            if msg['is_spam']:\n                trust_level_spam[trust_level].append(msg)\n        \n        # Spam rates by trust level\n        features['close_friend_spam_rate'] = len(trust_level_spam['close_friend']) / max(trust_level_total['close_friend'], 1)\n        features['friend_spam_rate'] = len(trust_level_spam['friend']) / max(trust_level_total['friend'], 1)\n        features['acquaintance_spam_rate'] = len(trust_level_spam['acquaintance']) / max(trust_level_total['acquaintance'], 1)\n        \n        # Trust abuse score analysis\n        trust_abuse_scores = [msg.get('trust_abuse_score', 0) for msg in user_messages]\n        features['avg_trust_abuse_score'] = np.mean(trust_abuse_scores)\n        features['max_trust_abuse_score'] = max(trust_abuse_scores)\n        features['high_trust_abuse_ratio'] = sum(1 for score in trust_abuse_scores if score > 0.5) / len(trust_abuse_scores)\n        \n        # Targeting pattern analysis\n        recipients = [msg['recipient_id'] for msg in user_messages if 'recipient_id' in msg]\n        if recipients:\n            recipient_counts = Counter(recipients)\n            features['recipient_diversity'] = len(set(recipients)) / len(recipients)\n            features['max_messages_to_single_recipient'] = max(recipient_counts.values())\n            \n            # Check if targeting high-influence users\n            high_influence_targets = 0\n            for recipient_id in set(recipients):\n                if recipient_id in users and users[recipient_id].get('social_influence', 0) > 0.6:\n                    high_influence_targets += 1\n            features['high_influence_targeting_ratio'] = high_influence_targets / len(set(recipients))\n        else:\n            features.update({\n                'recipient_diversity': 0,\n                'max_messages_to_single_recipient': 0,\n                'high_influence_targeting_ratio': 0\n            })\n        \n        return features\n    \n    def _extract_behavioral_change_features(self, user_id, user_messages, user_data):\n        \"\"\"Extract features that detect behavioral changes indicating compromise\"\"\"\n        features = {}\n        \n        if not user_messages or not user_data.get('compromise_date'):\n            return {f'behavioral_change_{i}': 0 for i in range(8)}  # Return zeros\n        \n        compromise_date = user_data['compromise_date']\n        \n        # Split messages into pre and post compromise\n        pre_messages = [msg for msg in user_messages if msg['timestamp'] < compromise_date]\n        post_messages = [msg for msg in user_messages if msg['timestamp'] >= compromise_date]\n        \n        if len(pre_messages) == 0 or len(post_messages) == 0:\n            return {f'behavioral_change_{i}': 0 for i in range(8)}\n        \n        # Compare spam rates\n        pre_spam_rate = sum(1 for msg in pre_messages if msg['is_spam']) / len(pre_messages)\n        post_spam_rate = sum(1 for msg in post_messages if msg['is_spam']) / len(post_messages)\n        features['spam_rate_change'] = post_spam_rate - pre_spam_rate\n        \n        # Compare messaging frequency\n        pre_days = max((compromise_date - min(msg['timestamp'] for msg in pre_messages)).days, 1)\n        post_days = max((max(msg['timestamp'] for msg in post_messages) - compromise_date).days, 1)\n        \n        pre_frequency = len(pre_messages) / pre_days\n        post_frequency = len(post_messages) / post_days\n        features['frequency_change_ratio'] = post_frequency / max(pre_frequency, 0.1)\n        \n        # Compare trust abuse patterns\n        pre_trust_abuse = np.mean([msg.get('trust_abuse_score', 0) for msg in pre_messages])\n        post_trust_abuse = np.mean([msg.get('trust_abuse_score', 0) for msg in post_messages])\n        features['trust_abuse_change'] = post_trust_abuse - pre_trust_abuse\n        \n        # Compare content patterns\n        pre_content = [msg['content'] for msg in pre_messages]\n        post_content = [msg['content'] for msg in post_messages]\n        \n        # Keyword usage change\n        pre_spam_keywords = sum(sum(1 for keyword in self.spam_keywords if keyword in content.lower()) \n                               for content in pre_content) / len(pre_content)\n        post_spam_keywords = sum(sum(1 for keyword in self.spam_keywords if keyword in content.lower()) \n                                for content in post_content) / len(post_content)\n        features['spam_keywords_change'] = post_spam_keywords - pre_spam_keywords\n        \n        # Message length change\n        pre_avg_length = np.mean([len(content.split()) for content in pre_content])\n        post_avg_length = np.mean([len(content.split()) for content in post_content])\n        features['message_length_change'] = post_avg_length - pre_avg_length\n        \n        # Timing pattern change\n        pre_hours = [msg['timestamp'].hour for msg in pre_messages]\n        post_hours = [msg['timestamp'].hour for msg in post_messages]\n        \n        pre_night_ratio = sum(1 for h in pre_hours if h < 6 or h > 22) / len(pre_hours)\n        post_night_ratio = sum(1 for h in post_hours if h < 6 or h > 22) / len(post_hours)\n        features['night_activity_change'] = post_night_ratio - pre_night_ratio\n        \n        return features\n    \n    def prepare_model_input(self, user_features):\n        \"\"\"Prepare features for model training\"\"\"\n        print(\"🔧 Preparing model input...\")\n        \n        # Combine all features into matrices\n        user_ids = list(user_features.keys())\n        labels = [user_features[uid]['label'] for uid in user_ids]\n        \n        # Separate feature types\n        structural_features = []\n        temporal_features = []\n        content_features = []\n        trust_abuse_features = []\n        behavioral_change_features = []\n        \n        for uid in user_ids:\n            features = user_features[uid]\n            structural_features.append(list(features['structural'].values()))\n            temporal_features.append(list(features['temporal'].values()))\n            content_features.append(list(features['content'].values()))\n            trust_abuse_features.append(list(features['trust_abuse'].values()))\n            behavioral_change_features.append(list(features['behavioral_change'].values()))\n        \n        # Convert to numpy arrays and normalize\n        scaler = StandardScaler()\n        \n        structural_matrix = scaler.fit_transform(np.array(structural_features))\n        temporal_matrix = scaler.fit_transform(np.array(temporal_features))\n        content_matrix = scaler.fit_transform(np.array(content_features))\n        trust_abuse_matrix = scaler.fit_transform(np.array(trust_abuse_features))\n        behavioral_change_matrix = scaler.fit_transform(np.array(behavioral_change_features))\n        \n        # Combine all features\n        combined_features = np.hstack([\n            structural_matrix,\n            temporal_matrix,\n            content_matrix,\n            trust_abuse_matrix,\n            behavioral_change_matrix\n        ])\n        \n        print(f\"✅ Prepared features: {combined_features.shape[1]} dimensions for {len(user_ids)} users\")\n        \n        return {\n            'user_ids': user_ids,\n            'features': combined_features,\n            'labels': np.array(labels),\n            'structural_features': structural_matrix,\n            'temporal_features': temporal_matrix,\n            'content_features': content_matrix,\n            'trust_abuse_features': trust_abuse_matrix,\n            'behavioral_change_features': behavioral_change_matrix\n        }\n\n# Usage example\nif __name__ == \"__main__\":\n    # This would be used with the compromised account dataset\n    print(\"🎯 Trust Abuse Feature Extractor ready for compromised account detection!\")\n    print(\"   Focus: Detecting accounts that spam their trusted connections\")\n    print(\"   Features: Structural, Temporal, Content, Trust Abuse, Behavioral Change\")
