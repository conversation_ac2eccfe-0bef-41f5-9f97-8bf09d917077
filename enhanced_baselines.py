"""
Enhanced Baselines for Graph-Based Spam Detection
Implements state-of-the-art methods for proper comparison with Behavior-Aware GAT
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, SAGEConv, GATConv, global_mean_pool
from torch_geometric.data import Data, Batch
import numpy as np
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, roc_auc_score
import networkx as nx

class GraphSAGE(nn.Module):
    """
    GraphSAGE implementation for spam detection
    Hamilton et al. "Inductive Representation Learning on Large Graphs" (2017)
    """
    def __init__(self, input_dim, hidden_dim=64, num_classes=2, num_layers=2, dropout=0.5):
        super(GraphSAGE, self).__init__()
        self.num_layers = num_layers
        self.dropout = dropout
        
        self.convs = nn.ModuleList()
        self.convs.append(SAGEConv(input_dim, hidden_dim))
        for _ in range(num_layers - 2):
            self.convs.append(SAGEConv(hidden_dim, hidden_dim))
        self.convs.append(SAGEConv(hidden_dim, hidden_dim))
        
        self.classifier = nn.Linear(hidden_dim, num_classes)
        self.dropout_layer = nn.Dropout(dropout)
        
    def forward(self, x, edge_index, batch=None):
        for i, conv in enumerate(self.convs):
            x = conv(x, edge_index)
            if i < len(self.convs) - 1:
                x = F.relu(x)
                x = self.dropout_layer(x)
        
        x = self.classifier(x)
        return x

class EnhancedGCN(nn.Module):
    """
    Enhanced Graph Convolutional Network with residual connections
    Based on Kipf & Welling "Semi-Supervised Classification with Graph Convolutional Networks" (2016)
    """
    def __init__(self, input_dim, hidden_dim=64, num_classes=2, num_layers=3, dropout=0.5):
        super(EnhancedGCN, self).__init__()
        self.num_layers = num_layers
        self.dropout = dropout
        
        self.convs = nn.ModuleList()
        self.batch_norms = nn.ModuleList()
        
        # Input layer
        self.convs.append(GCNConv(input_dim, hidden_dim))
        self.batch_norms.append(nn.BatchNorm1d(hidden_dim))
        
        # Hidden layers
        for _ in range(num_layers - 2):
            self.convs.append(GCNConv(hidden_dim, hidden_dim))
            self.batch_norms.append(nn.BatchNorm1d(hidden_dim))
            
        # Output layer
        self.convs.append(GCNConv(hidden_dim, hidden_dim))
        self.batch_norms.append(nn.BatchNorm1d(hidden_dim))
        
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, num_classes)
        )
        
        self.dropout_layer = nn.Dropout(dropout)
        
    def forward(self, x, edge_index, batch=None):
        residual = None
        
        for i, (conv, bn) in enumerate(zip(self.convs, self.batch_norms)):
            x = conv(x, edge_index)
            x = bn(x)
            
            if i > 0 and residual is not None and x.shape == residual.shape:
                x = x + residual  # Residual connection
                
            if i < len(self.convs) - 1:
                residual = x
                x = F.relu(x)
                x = self.dropout_layer(x)
        
        x = self.classifier(x)
        return x

class MultiViewGAT(nn.Module):
    """
    Multi-view Graph Attention Network
    Processes different feature views separately then combines
    """
    def __init__(self, content_dim, temporal_dim, structural_dim, 
                 hidden_dim=64, num_heads=4, num_classes=2, dropout=0.3):
        super(MultiViewGAT, self).__init__()
        
        # Separate GAT layers for each view
        self.content_gat = GATConv(content_dim, hidden_dim, heads=num_heads, dropout=dropout, concat=False)
        self.temporal_gat = GATConv(temporal_dim, hidden_dim, heads=num_heads, dropout=dropout, concat=False)
        self.structural_gat = GATConv(structural_dim, hidden_dim, heads=num_heads, dropout=dropout, concat=False)
        
        # Fusion layer
        self.fusion = nn.Sequential(
            nn.Linear(3 * hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, num_classes)
        )
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, data):
        content_features = data.content_features
        temporal_features = data.temporal_features
        structural_features = data.structural_features
        edge_index = data.edge_index
        
        # Process each view separately
        content_out = F.relu(self.content_gat(content_features, edge_index))
        temporal_out = F.relu(self.temporal_gat(temporal_features, edge_index))
        structural_out = F.relu(self.structural_gat(structural_features, edge_index))
        
        # Concatenate and fuse
        combined = torch.cat([content_out, temporal_out, structural_out], dim=1)
        combined = self.dropout(combined)
        
        output = self.fusion(combined)
        return output

class HeterogeneousGNN(nn.Module):
    """
    Simplified Heterogeneous Graph Neural Network
    Inspired by Wang et al. "Heterogeneous Graph Attention Network" (2019)
    """
    def __init__(self, input_dim, hidden_dim=64, num_classes=2, dropout=0.5):
        super(HeterogeneousGNN, self).__init__()
        
        # Different transformations for different node types
        self.user_transform = nn.Linear(input_dim, hidden_dim)
        self.message_transform = nn.Linear(input_dim, hidden_dim)
        
        # GAT layers for different edge types
        self.user_gat = GATConv(hidden_dim, hidden_dim, heads=4, dropout=dropout, concat=False)
        self.message_gat = GATConv(hidden_dim, hidden_dim, heads=4, dropout=dropout, concat=False)
        
        # Final classifier
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, num_classes)
        )
        
    def forward(self, x, edge_index, node_type=None):
        # For simplicity, treat all nodes as users in this implementation
        x = self.user_transform(x)
        x = F.relu(self.user_gat(x, edge_index))
        x = self.classifier(x)
        return x

class EnsembleGraphModel(nn.Module):
    """
    Ensemble of different graph neural networks
    """
    def __init__(self, input_dim, hidden_dim=64, num_classes=2, dropout=0.5):
        super(EnsembleGraphModel, self).__init__()
        
        self.gcn = EnhancedGCN(input_dim, hidden_dim, num_classes, dropout=dropout)
        self.sage = GraphSAGE(input_dim, hidden_dim, num_classes, dropout=dropout)
        self.gat = GATConv(input_dim, hidden_dim, heads=4, dropout=dropout, concat=False)
        
        self.final_classifier = nn.Linear(hidden_dim + num_classes * 2, num_classes)
        
    def forward(self, x, edge_index):
        gcn_out = self.gcn(x, edge_index)
        sage_out = self.sage(x, edge_index)
        gat_out = F.relu(self.gat(x, edge_index))
        
        # Combine outputs
        combined = torch.cat([gat_out, gcn_out, sage_out], dim=1)
        final_out = self.final_classifier(combined)
        
        return final_out

def create_enhanced_baselines():
    """
    Factory function to create all baseline models
    """
    baselines = {
        'GraphSAGE': GraphSAGE,
        'Enhanced_GCN': EnhancedGCN,
        'MultiView_GAT': MultiViewGAT,
        'Heterogeneous_GNN': HeterogeneousGNN,
        'Ensemble_Graph': EnsembleGraphModel,
        
        # Traditional ML baselines (improved)
        'Random_Forest': lambda: RandomForestClassifier(n_estimators=200, max_depth=10, random_state=42),
        'Gradient_Boosting': lambda: GradientBoostingClassifier(n_estimators=100, learning_rate=0.1, random_state=42),
        'SVM_RBF': lambda: SVC(kernel='rbf', C=1.0, gamma='scale', probability=True, random_state=42),
        'Logistic_Regression': lambda: LogisticRegression(C=1.0, max_iter=1000, random_state=42),
        'MLP_Enhanced': lambda: MLPClassifier(hidden_layer_sizes=(128, 64, 32), 
                                            activation='relu', solver='adam', 
                                            alpha=0.001, max_iter=500, random_state=42)
    }
    
    return baselines

def evaluate_model(model, data, train_mask, test_mask, model_type='graph'):
    """
    Evaluate a model and return comprehensive metrics
    """
    if model_type == 'graph':
        model.eval()
        with torch.no_grad():
            if hasattr(data, 'content_features'):  # MultiView model
                out = model(data)
            else:
                out = model(data.x, data.edge_index)
            
            pred = out[test_mask].argmax(dim=1)
            y_true = data.y[test_mask]
            
            accuracy = (pred == y_true).float().mean().item()
            
            # Get probabilities for AUC
            probs = F.softmax(out[test_mask], dim=1)[:, 1]
            auc = roc_auc_score(y_true.cpu(), probs.cpu())
            
            precision, recall, f1, _ = precision_recall_fscore_support(
                y_true.cpu(), pred.cpu(), average='weighted'
            )
            
    else:  # Traditional ML
        X_train = data.x[train_mask].cpu().numpy()
        X_test = data.x[test_mask].cpu().numpy()
        y_train = data.y[train_mask].cpu().numpy()
        y_test = data.y[test_mask].cpu().numpy()
        
        model.fit(X_train, y_train)
        pred = model.predict(X_test)
        pred_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else pred
        
        accuracy = accuracy_score(y_test, pred)
        auc = roc_auc_score(y_test, pred_proba)
        precision, recall, f1, _ = precision_recall_fscore_support(y_test, pred, average='weighted')
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'auc': auc
    }
