"""
State-of-the-Art Spam Detection Baselines
Implements current best methods for comparison with Behavior-Aware GAT
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, GraphSAGE, TransformerConv
from torch_geometric.nn import global_mean_pool, global_max_pool
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from transformers import BertModel, BertTokenizer
import warnings
warnings.filterwarnings('ignore')

class BERTSpamDetector(nn.Module):
    """
    BERT-based spam detection (content-only baseline)
    State-of-the-art for text classification
    """
    def __init__(self, bert_model_name='bert-base-uncased', num_classes=2):
        super().__init__()
        self.bert = BertModel.from_pretrained(bert_model_name)
        self.dropout = nn.Dropout(0.3)
        self.classifier = nn.Linear(self.bert.config.hidden_size, num_classes)
        
    def forward(self, input_ids, attention_mask):
        outputs = self.bert(input_ids=input_ids, attention_mask=attention_mask)
        pooled_output = outputs.pooler_output
        output = self.dropout(pooled_output)
        return self.classifier(output)

class GraphSAGESpamDetector(nn.Module):
    """
    GraphSAGE for spam detection
    State-of-the-art graph neural network baseline
    """
    def __init__(self, input_dim, hidden_dim=128, num_layers=2, num_classes=2):
        super().__init__()
        self.num_layers = num_layers
        self.convs = nn.ModuleList()
        
        # First layer
        self.convs.append(GraphSAGE(input_dim, hidden_dim, num_layers=1))
        
        # Additional layers
        for _ in range(num_layers - 1):
            self.convs.append(GraphSAGE(hidden_dim, hidden_dim, num_layers=1))
        
        self.classifier = nn.Linear(hidden_dim, num_classes)
        self.dropout = nn.Dropout(0.5)
        
    def forward(self, x, edge_index, batch=None):
        for conv in self.convs:
            x = conv(x, edge_index)
            x = F.relu(x)
            x = self.dropout(x)
        
        # Global pooling if batch is provided
        if batch is not None:
            x = global_mean_pool(x, batch)
        
        return self.classifier(x)

class EnhancedGCN(nn.Module):
    """
    Enhanced Graph Convolutional Network
    Multi-layer GCN with attention and residual connections
    """
    def __init__(self, input_dim, hidden_dim=128, num_layers=3, num_classes=2):
        super().__init__()
        self.num_layers = num_layers
        self.convs = nn.ModuleList()
        self.batch_norms = nn.ModuleList()
        
        # Input layer
        self.convs.append(GCNConv(input_dim, hidden_dim))
        self.batch_norms.append(nn.BatchNorm1d(hidden_dim))
        
        # Hidden layers
        for _ in range(num_layers - 2):
            self.convs.append(GCNConv(hidden_dim, hidden_dim))
            self.batch_norms.append(nn.BatchNorm1d(hidden_dim))
        
        # Output layer
        self.convs.append(GCNConv(hidden_dim, hidden_dim))
        self.batch_norms.append(nn.BatchNorm1d(hidden_dim))
        
        self.classifier = nn.Linear(hidden_dim, num_classes)
        self.dropout = nn.Dropout(0.5)
        
    def forward(self, x, edge_index, batch=None):
        residual = None
        
        for i, (conv, bn) in enumerate(zip(self.convs, self.batch_norms)):
            x = conv(x, edge_index)
            x = bn(x)
            x = F.relu(x)
            
            # Residual connection every 2 layers
            if i > 0 and i % 2 == 0 and residual is not None:
                x = x + residual
            
            if i % 2 == 0:
                residual = x
            
            x = self.dropout(x)
        
        if batch is not None:
            x = global_mean_pool(x, batch)
        
        return self.classifier(x)

class MultiViewGAT(nn.Module):
    """
    Multi-View Graph Attention Network
    Processes different feature types with separate GAT layers
    """
    def __init__(self, content_dim, structural_dim, temporal_dim, hidden_dim=64, num_classes=2):
        super().__init__()
        
        # Separate GAT layers for each view
        self.content_gat = GATConv(content_dim, hidden_dim, heads=4, dropout=0.3)
        self.structural_gat = GATConv(structural_dim, hidden_dim, heads=4, dropout=0.3)
        self.temporal_gat = GATConv(temporal_dim, hidden_dim, heads=4, dropout=0.3)
        
        # Fusion layer
        self.fusion = nn.Linear(hidden_dim * 3 * 4, hidden_dim)  # 4 heads
        self.classifier = nn.Linear(hidden_dim, num_classes)
        self.dropout = nn.Dropout(0.5)
        
    def forward(self, content_features, structural_features, temporal_features, edge_index, batch=None):
        # Process each view
        content_out = self.content_gat(content_features, edge_index)
        structural_out = self.structural_gat(structural_features, edge_index)
        temporal_out = self.temporal_gat(temporal_features, edge_index)
        
        # Concatenate and fuse
        combined = torch.cat([content_out, structural_out, temporal_out], dim=1)
        fused = F.relu(self.fusion(combined))
        fused = self.dropout(fused)
        
        if batch is not None:
            fused = global_mean_pool(fused, batch)
        
        return self.classifier(fused)

class TransformerGNN(nn.Module):
    """
    Transformer-based Graph Neural Network
    Uses graph transformer layers for spam detection
    """
    def __init__(self, input_dim, hidden_dim=128, num_layers=2, num_heads=4, num_classes=2):
        super().__init__()
        self.num_layers = num_layers
        
        self.convs = nn.ModuleList()
        for _ in range(num_layers):
            self.convs.append(TransformerConv(
                input_dim if _ == 0 else hidden_dim,
                hidden_dim,
                heads=num_heads,
                dropout=0.3
            ))
        
        self.classifier = nn.Linear(hidden_dim, num_classes)
        self.dropout = nn.Dropout(0.5)
        
    def forward(self, x, edge_index, batch=None):
        for conv in self.convs:
            x = conv(x, edge_index)
            x = F.relu(x)
            x = self.dropout(x)
        
        if batch is not None:
            x = global_mean_pool(x, batch)
        
        return self.classifier(x)

class HierarchicalGAT(nn.Module):
    """
    Hierarchical Graph Attention Network
    Multi-level attention mechanism
    """
    def __init__(self, input_dim, hidden_dim=64, num_classes=2):
        super().__init__()
        
        # Node-level attention
        self.node_gat1 = GATConv(input_dim, hidden_dim, heads=4, dropout=0.3)
        self.node_gat2 = GATConv(hidden_dim * 4, hidden_dim, heads=4, dropout=0.3)
        
        # Graph-level attention
        self.graph_attention = nn.MultiheadAttention(hidden_dim * 4, num_heads=4, dropout=0.3)
        
        self.classifier = nn.Linear(hidden_dim * 4, num_classes)
        self.dropout = nn.Dropout(0.5)
        
    def forward(self, x, edge_index, batch=None):
        # Node-level processing
        x = self.node_gat1(x, edge_index)
        x = F.relu(x)
        x = self.dropout(x)
        
        x = self.node_gat2(x, edge_index)
        x = F.relu(x)
        
        # Graph-level attention
        if batch is not None:
            # Reshape for attention
            x_reshaped = x.unsqueeze(0)  # Add sequence dimension
            attended, _ = self.graph_attention(x_reshaped, x_reshaped, x_reshaped)
            x = attended.squeeze(0)
            
            x = global_mean_pool(x, batch)
        
        return self.classifier(x)

class EnsembleSpamDetector(nn.Module):
    """
    Ensemble of multiple state-of-the-art methods
    Combines predictions from different models
    """
    def __init__(self, models, weights=None):
        super().__init__()
        self.models = nn.ModuleList(models)
        self.weights = weights if weights else [1.0] * len(models)
        self.weights = torch.tensor(self.weights) / sum(self.weights)
        
    def forward(self, *args, **kwargs):
        predictions = []
        
        for model in self.models:
            pred = model(*args, **kwargs)
            predictions.append(F.softmax(pred, dim=-1))
        
        # Weighted ensemble
        ensemble_pred = torch.zeros_like(predictions[0])
        for i, pred in enumerate(predictions):
            ensemble_pred += self.weights[i] * pred
        
        return torch.log(ensemble_pred + 1e-8)  # Convert back to log probabilities

class StateOfArtBaselines:
    """
    Collection of state-of-the-art spam detection baselines
    """
    
    def __init__(self):
        self.models = {}
        
    def get_bert_baseline(self):
        """Get BERT-based spam detector"""
        return BERTSpamDetector()
    
    def get_graphsage_baseline(self, input_dim):
        """Get GraphSAGE baseline"""
        return GraphSAGESpamDetector(input_dim)
    
    def get_enhanced_gcn_baseline(self, input_dim):
        """Get Enhanced GCN baseline"""
        return EnhancedGCN(input_dim)
    
    def get_multiview_gat_baseline(self, content_dim, structural_dim, temporal_dim):
        """Get Multi-View GAT baseline"""
        return MultiViewGAT(content_dim, structural_dim, temporal_dim)
    
    def get_transformer_gnn_baseline(self, input_dim):
        """Get Transformer GNN baseline"""
        return TransformerGNN(input_dim)
    
    def get_hierarchical_gat_baseline(self, input_dim):
        """Get Hierarchical GAT baseline"""
        return HierarchicalGAT(input_dim)
    
    def get_ensemble_baseline(self, models, weights=None):
        """Get ensemble of multiple models"""
        return EnsembleSpamDetector(models, weights)
    
    def get_traditional_ml_baselines(self):
        """Get traditional ML baselines for comparison"""
        return {
            'random_forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'svm': SVC(kernel='rbf', random_state=42),
            'logistic_regression': LogisticRegression(random_state=42)
        }
    
    def get_all_baselines(self, content_dim, structural_dim, temporal_dim):
        """Get all baseline models for comprehensive comparison"""
        total_dim = content_dim + structural_dim + temporal_dim
        
        baselines = {
            'bert': self.get_bert_baseline(),
            'graphsage': self.get_graphsage_baseline(total_dim),
            'enhanced_gcn': self.get_enhanced_gcn_baseline(total_dim),
            'multiview_gat': self.get_multiview_gat_baseline(content_dim, structural_dim, temporal_dim),
            'transformer_gnn': self.get_transformer_gnn_baseline(total_dim),
            'hierarchical_gat': self.get_hierarchical_gat_baseline(total_dim)
        }
        
        # Add traditional ML for reference
        baselines.update(self.get_traditional_ml_baselines())
        
        return baselines

# Baseline performance expectations
EXPECTED_BASELINE_PERFORMANCE = {
    'bert': {'accuracy': (0.72, 0.78), 'description': 'Content-only, misses network patterns'},
    'graphsage': {'accuracy': (0.75, 0.82), 'description': 'Good network modeling, limited behavioral analysis'},
    'enhanced_gcn': {'accuracy': (0.73, 0.80), 'description': 'Enhanced GCN with residual connections'},
    'multiview_gat': {'accuracy': (0.78, 0.85), 'description': 'Multi-view processing but no behavioral fusion'},
    'transformer_gnn': {'accuracy': (0.76, 0.83), 'description': 'Transformer attention on graphs'},
    'hierarchical_gat': {'accuracy': (0.77, 0.84), 'description': 'Hierarchical attention mechanism'},
    'random_forest': {'accuracy': (0.65, 0.72), 'description': 'Traditional ML baseline'},
    'svm': {'accuracy': (0.63, 0.70), 'description': 'Traditional ML baseline'},
    'logistic_regression': {'accuracy': (0.60, 0.68), 'description': 'Traditional ML baseline'}
}

# Usage example
if __name__ == "__main__":
    print("🔬 State-of-the-Art Spam Detection Baselines")
    print("Available models:")
    
    baselines = StateOfArtBaselines()
    
    # Example dimensions
    content_dim, structural_dim, temporal_dim = 25, 15, 20
    
    all_models = baselines.get_all_baselines(content_dim, structural_dim, temporal_dim)
    
    for name, model in all_models.items():
        if hasattr(model, 'parameters'):  # Neural network models
            total_params = sum(p.numel() for p in model.parameters())
            print(f"  {name}: {total_params:,} parameters")
        else:  # Traditional ML models
            print(f"  {name}: Traditional ML model")
    
    print("\\n📊 Expected Performance Ranges:")
    for name, perf in EXPECTED_BASELINE_PERFORMANCE.items():
        acc_range = perf['accuracy']
        desc = perf['description']
        print(f"  {name}: {acc_range[0]*100:.1f}-{acc_range[1]*100:.1f}% - {desc}")
