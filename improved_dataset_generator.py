"""
Improved Realistic Dataset Generator
Addresses data leakage issues and creates truly challenging scenarios
"""

import numpy as np
import pandas as pd
import torch
from datetime import datetime, timedelta
import random
import networkx as nx
from sklearn.preprocessing import StandardScaler
from transformers import pipeline
import re
import warnings
warnings.filterwarnings('ignore')

class ImprovedDatasetGenerator:
    """
    Generate realistic social network data that prevents data leakage
    and creates appropriate challenge level for advanced methods
    """
    
    def __init__(self, num_users=3000, num_messages=12000, compromise_rate=0.08):
        self.num_users = num_users
        self.num_messages = num_messages
        self.compromise_rate = compromise_rate
        
        # Initialize text generation pipeline for more realistic content
        self.text_generator = pipeline("text-generation", 
                                     model="gpt2", 
                                     max_length=50, 
                                     do_sample=True,
                                     temperature=0.7)
        
        # Realistic base vocabulary (shared between legitimate and spam)
        self.base_vocabulary = {
            'emotions': ['amazing', 'great', 'awesome', 'fantastic', 'incredible', 'wonderful', 'excellent'],
            'actions': ['check out', 'try', 'discover', 'explore', 'experience', 'enjoy', 'love'],
            'objects': ['product', 'service', 'opportunity', 'experience', 'solution', 'method', 'system'],
            'benefits': ['save money', 'save time', 'improve life', 'get results', 'achieve goals', 'feel better'],
            'urgency': ['limited time', 'don\'t miss', 'act now', 'hurry', 'last chance', 'exclusive'],
            'social_proof': ['everyone loves', 'highly recommended', 'proven results', 'thousands use', 'bestselling']
        }
        
        # Compromise attack patterns (realistic scenarios)
        self.attack_patterns = {
            'credential_stuffing': {'probability': 0.4, 'detection_difficulty': 'medium'},
            'phishing_victim': {'probability': 0.3, 'detection_difficulty': 'hard'},
            'malware_infection': {'probability': 0.2, 'detection_difficulty': 'easy'},
            'social_engineering': {'probability': 0.1, 'detection_difficulty': 'very_hard'}
        }
        
    def generate_realistic_content(self, is_spam=False, user_context=None):
        """Generate realistic content that doesn't rely on obvious templates"""
        
        # Base content generation using shared vocabulary
        emotion = np.random.choice(self.base_vocabulary['emotions'])
        action = np.random.choice(self.base_vocabulary['actions'])
        obj = np.random.choice(self.base_vocabulary['objects'])
        
        if is_spam:
            # Spam content uses more persuasive language but not obvious templates
            benefit = np.random.choice(self.base_vocabulary['benefits'])
            urgency = np.random.choice(self.base_vocabulary['urgency'])
            social_proof = np.random.choice(self.base_vocabulary['social_proof'])
            
            # Construct spam message with subtle persuasion patterns
            patterns = [
                f"Just discovered this {emotion} {obj} that helps you {benefit}. {social_proof}!",
                f"You should {action} this {obj} - it's {emotion} and {urgency}.",
                f"This {obj} is {emotion}! {social_proof} and it really helps {benefit}.",
                f"Found an {emotion} way to {benefit}. {urgency} - {action} this {obj}!"
            ]
            content = np.random.choice(patterns)
            
            # Add subtle spam indicators (not obvious)
            if np.random.random() < 0.3:  # Only sometimes
                content += " DM for details."
            if np.random.random() < 0.2:  # Rarely
                content += f" Use code SAVE{np.random.randint(10,50)} for discount."
                
        else:
            # Legitimate content - can also use persuasive language sometimes
            topics = ['work', 'family', 'hobbies', 'food', 'travel', 'books', 'movies', 'fitness']
            topic = np.random.choice(topics)
            
            patterns = [
                f"Had an {emotion} day at {topic} today!",
                f"Just finished {topic} - feeling {emotion}!",
                f"Love this new {topic} {obj} I discovered. Really {emotion}!",
                f"Weekend {topic} plans are going to be {emotion}!",
                f"This {topic} experience was {emotion}. Highly recommend!"
            ]
            content = np.random.choice(patterns)
            
            # Legitimate users can also occasionally sound promotional (false positives)
            if np.random.random() < 0.05:  # 5% chance
                content += f" You should {action} it too!"
        
        return content
    
    def generate_realistic_users(self):
        """Generate users with realistic but non-obvious behavioral patterns"""
        users = {}
        
        # More nuanced user types
        user_archetypes = {
            'casual_young': {'age_range': (18, 25), 'activity_base': 8, 'tech_savvy': 0.7},
            'casual_adult': {'age_range': (26, 45), 'activity_base': 5, 'tech_savvy': 0.5},
            'casual_senior': {'age_range': (46, 70), 'activity_base': 3, 'tech_savvy': 0.3},
            'professional': {'age_range': (25, 55), 'activity_base': 6, 'tech_savvy': 0.8},
            'influencer': {'age_range': (20, 40), 'activity_base': 20, 'tech_savvy': 0.9},
            'business': {'age_range': (30, 60), 'activity_base': 10, 'tech_savvy': 0.6},
            'student': {'age_range': (18, 25), 'activity_base': 12, 'tech_savvy': 0.9}
        }
        
        for i in range(self.num_users):
            user_id = f\"user_{i}\"\n            \n            # Assign archetype\n            archetype_name = np.random.choice(list(user_archetypes.keys()))\n            archetype = user_archetypes[archetype_name]\n            \n            # Generate user attributes\n            age = np.random.randint(*archetype['age_range'])\n            tech_savvy = archetype['tech_savvy'] + np.random.normal(0, 0.1)\n            tech_savvy = np.clip(tech_savvy, 0, 1)\n            \n            # Account age (older users tend to have older accounts)\n            if age > 45:\n                account_age_days = np.random.randint(365, 2190)  # 1-6 years\n            elif age > 25:\n                account_age_days = np.random.randint(180, 1460)  # 6 months - 4 years\n            else:\n                account_age_days = np.random.randint(30, 1095)   # 1 month - 3 years\n            \n            # Activity patterns based on age and archetype\n            base_activity = archetype['activity_base']\n            activity_variance = base_activity * 0.3\n            \n            # Generate weekly pattern with realistic variations\n            weekly_pattern = []\n            for day in range(7):\n                if day < 5:  # Weekdays\n                    if archetype_name in ['professional', 'business']:\n                        day_activity = int(base_activity * np.random.uniform(1.2, 1.5))\n                    else:\n                        day_activity = int(base_activity * np.random.uniform(0.8, 1.2))\n                else:  # Weekends\n                    if archetype_name in ['student', 'casual_young']:\n                        day_activity = int(base_activity * np.random.uniform(1.3, 1.8))\n                    else:\n                        day_activity = int(base_activity * np.random.uniform(0.6, 1.4))\n                weekly_pattern.append(max(0, day_activity))\n            \n            # Compromise vulnerability based on realistic factors\n            vulnerability_score = 0.0\n            \n            # Age factor (older users more vulnerable to some attacks)\n            if age > 50:\n                vulnerability_score += 0.3\n            elif age < 25:\n                vulnerability_score += 0.2  # Young users also vulnerable\n            \n            # Tech savviness (lower = more vulnerable)\n            vulnerability_score += (1 - tech_savvy) * 0.4\n            \n            # Account age (newer accounts more vulnerable)\n            if account_age_days < 90:\n                vulnerability_score += 0.2\n            \n            # Activity level (very high or very low activity = more vulnerable)\n            activity_score = sum(weekly_pattern) / 7\n            if activity_score > 15 or activity_score < 2:\n                vulnerability_score += 0.1\n            \n            # Random factor to prevent perfect prediction\n            vulnerability_score += np.random.uniform(-0.2, 0.2)\n            vulnerability_score = np.clip(vulnerability_score, 0, 1)\n            \n            # Determine compromise based on vulnerability and random chance\n            compromise_threshold = self.compromise_rate * 2  # Adjust threshold\n            is_compromised = vulnerability_score > (1 - compromise_threshold)\n            \n            # If compromised, select attack pattern\n            attack_pattern = None\n            if is_compromised:\n                attack_pattern = np.random.choice(\n                    list(self.attack_patterns.keys()),\n                    p=[0.4, 0.3, 0.2, 0.1]  # Probabilities for each attack type\n                )\n            \n            # Generate follower counts based on archetype and age\n            if archetype_name == 'influencer':\n                follower_count = np.random.randint(1000, 50000)\n            elif archetype_name == 'business':\n                follower_count = np.random.randint(500, 10000)\n            elif archetype_name == 'professional':\n                follower_count = np.random.randint(200, 2000)\n            else:\n                follower_count = np.random.randint(50, 1000)\n            \n            following_count = int(follower_count * np.random.uniform(0.1, 0.8))\n            \n            users[user_id] = {\n                'user_id': user_id,\n                'archetype': archetype_name,\n                'age': age,\n                'tech_savvy': tech_savvy,\n                'account_age_days': account_age_days,\n                'follower_count': follower_count,\n                'following_count': following_count,\n                'weekly_activity_pattern': weekly_pattern,\n                'vulnerability_score': vulnerability_score,\n                'is_compromised': is_compromised,\n                'attack_pattern': attack_pattern,\n                'compromise_date': datetime.now() - timedelta(days=np.random.randint(1, 30)) if is_compromised else None\n            }\n        \n        return users\n    \n    def generate_realistic_messages(self, users):\n        \"\"\"Generate messages with realistic content distribution\"\"\"\n        messages = []\n        \n        for i in range(self.num_messages):\n            # Select user based on their activity level (more active users post more)\n            user_weights = [sum(users[uid]['weekly_activity_pattern']) for uid in users.keys()]\n            user_weights = np.array(user_weights) / sum(user_weights)\n            \n            selected_user_idx = np.random.choice(len(users), p=user_weights)\n            user_id = f\"user_{selected_user_idx}\"\n            user = users[user_id]\n            \n            # Determine spam probability based on compromise status and attack pattern\n            if user['is_compromised']:\n                attack_pattern = user['attack_pattern']\n                \n                # Different attack patterns have different spam rates\n                if attack_pattern == 'credential_stuffing':\n                    spam_prob = 0.15  # Moderate spam\n                elif attack_pattern == 'phishing_victim':\n                    spam_prob = 0.25  # Higher spam\n                elif attack_pattern == 'malware_infection':\n                    spam_prob = 0.35  # High spam\n                elif attack_pattern == 'social_engineering':\n                    spam_prob = 0.10  # Low spam (more sophisticated)\n                else:\n                    spam_prob = 0.20\n            else:\n                # Legitimate users can occasionally post promotional content\n                if user['archetype'] in ['business', 'influencer']:\n                    spam_prob = 0.05  # 5% promotional content\n                else:\n                    spam_prob = 0.01  # 1% false positives\n            \n            is_spam = np.random.random() < spam_prob\n            \n            # Generate content\n            content = self.generate_realistic_content(is_spam, user)\n            \n            # Generate timestamp with realistic patterns\n            base_time = datetime.now() - timedelta(hours=np.random.randint(0, 168))\n            \n            # Apply user-specific timing patterns (subtle)\n            if user['is_compromised']:\n                # Compromised accounts might have slightly different timing\n                if user['attack_pattern'] == 'malware_infection':\n                    # Malware might post at unusual times\n                    hour_offset = np.random.normal(0, 4)  # More variance\n                else:\n                    hour_offset = np.random.normal(0, 2)  # Slight variance\n                base_time += timedelta(hours=hour_offset)\n            \n            messages.append({\n                'message_id': f\"msg_{i}\",\n                'user_id': user_id,\n                'content': content,\n                'timestamp': base_time,\n                'is_spam': is_spam,\n                'is_user_compromised': user['is_compromised'],\n                'attack_pattern': user['attack_pattern'] if user['is_compromised'] else None\n            })\n        \n        return messages\n    \n    def generate_realistic_network(self, users):\n        \"\"\"Generate realistic social network with compromise propagation\"\"\"\n        interactions = []\n        user_list = list(users.keys())\n        \n        # Create network using preferential attachment and homophily\n        G = nx.Graph()\n        G.add_nodes_from(user_list)\n        \n        # Add edges based on realistic social patterns\n        for user_id, user_data in users.items():\n            archetype = user_data['archetype']\n            age = user_data['age']\n            follower_count = user_data['follower_count']\n            \n            # Determine connection probability based on user characteristics\n            if archetype == 'influencer':\n                num_connections = min(len(user_list) - 1, int(follower_count * 0.05))\n            elif archetype == 'business':\n                num_connections = min(len(user_list) - 1, int(follower_count * 0.08))\n            else:\n                num_connections = min(len(user_list) - 1, int(follower_count * 0.15))\n            \n            # Select connections based on homophily (similar users connect)\n            potential_connections = []\n            for other_id, other_data in users.items():\n                if other_id == user_id:\n                    continue\n                \n                # Calculate similarity score\n                age_similarity = 1 - abs(age - other_data['age']) / 50\n                archetype_similarity = 1 if archetype == other_data['archetype'] else 0.3\n                \n                similarity_score = (age_similarity + archetype_similarity) / 2\n                potential_connections.append((other_id, similarity_score))\n            \n            # Sort by similarity and add some randomness\n            potential_connections.sort(key=lambda x: x[1] + np.random.uniform(0, 0.3), reverse=True)\n            \n            # Select top connections\n            selected_connections = potential_connections[:num_connections]\n            \n            # Add edges\n            for connected_user, similarity in selected_connections:\n                if not G.has_edge(user_id, connected_user):\n                    # Weight based on similarity and interaction frequency\n                    weight = int(similarity * 10) + np.random.randint(1, 5)\n                    G.add_edge(user_id, connected_user, weight=weight)\n                    interactions.append((user_id, connected_user, weight))\n        \n        return interactions, G\n    \n    def generate_complete_dataset(self):\n        \"\"\"Generate complete realistic dataset\"\"\"\n        print(f\"🔬 Generating improved realistic dataset with {self.num_users} users...\")\n        \n        # Generate users\n        users = self.generate_realistic_users()\n        compromised_count = sum(1 for u in users.values() if u['is_compromised'])\n        print(f\"  📊 Generated {len(users)} users ({compromised_count} compromised, {compromised_count/len(users)*100:.1f}%)\")\n        \n        # Generate messages\n        messages = self.generate_realistic_messages(users)\n        spam_count = sum(1 for m in messages if m['is_spam'])\n        print(f\"  💬 Generated {len(messages)} messages ({spam_count} spam, {spam_count/len(messages)*100:.1f}%)\")\n        \n        # Generate network\n        interactions, network_graph = self.generate_realistic_network(users)\n        print(f\"  🌐 Generated network with {len(interactions)} connections\")\n        \n        # Calculate attack pattern distribution\n        attack_patterns = {}\n        for user in users.values():\n            if user['attack_pattern']:\n                attack_patterns[user['attack_pattern']] = attack_patterns.get(user['attack_pattern'], 0) + 1\n        \n        print(f\"  🎯 Attack pattern distribution: {attack_patterns}\")\n        \n        return {\n            'users': users,\n            'messages': messages,\n            'interactions': interactions,\n            'network_graph': network_graph,\n            'statistics': {\n                'num_users': len(users),\n                'num_compromised': compromised_count,\n                'compromise_rate': compromised_count / len(users),\n                'num_messages': len(messages),\n                'num_spam': spam_count,\n                'spam_rate': spam_count / len(messages),\n                'num_connections': len(interactions),\n                'network_density': len(interactions) / (len(users) * (len(users) - 1) / 2),\n                'attack_patterns': attack_patterns\n            }\n        }\n\n# Usage example\nif __name__ == \"__main__\":\n    generator = ImprovedDatasetGenerator(num_users=2000, num_messages=8000)\n    dataset = generator.generate_complete_dataset()\n    print(\"\\n📈 Dataset Statistics:\")\n    for key, value in dataset['statistics'].items():\n        print(f\"  {key}: {value}\")
