"""
Comprehensive Evaluation Framework
Statistical significance testing, detailed cross-validation, and computational complexity analysis
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.metrics import confusion_matrix, classification_report
from scipy import stats
import time
import psutil
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

class ComprehensiveEvaluator:
    """
    Comprehensive evaluation framework for spam detection methods
    Includes statistical significance testing and computational complexity analysis
    """
    
    def __init__(self, random_state=42):
        self.random_state = random_state
        self.results = defaultdict(list)
        self.statistical_tests = {}
        self.complexity_analysis = {}
        
    def stratified_cross_validation(self, model, X, y, cv_folds=5, n_runs=10):
        """
        Perform stratified cross-validation with multiple runs
        """
        print(f"🔄 Running {n_runs} rounds of {cv_folds}-fold cross-validation...")
        
        all_scores = {
            'accuracy': [],
            'precision': [],
            'recall': [],
            'f1': [],
            'auc': []
        }
        
        for run in range(n_runs):
            print(f"  Run {run+1}/{n_runs}", end='\r')
            
            # Create stratified folds
            skf = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=self.random_state + run)
            
            run_scores = {metric: [] for metric in all_scores.keys()}
            
            for fold, (train_idx, val_idx) in enumerate(skf.split(X, y)):
                X_train, X_val = X[train_idx], X[val_idx]
                y_train, y_val = y[train_idx], y[val_idx]
                
                # Train model
                if hasattr(model, 'fit'):  # Traditional ML
                    model.fit(X_train, y_train)
                    y_pred = model.predict(X_val)
                    y_pred_proba = model.predict_proba(X_val)[:, 1] if hasattr(model, 'predict_proba') else y_pred
                else:  # Neural network
                    # This would need to be implemented based on your specific training loop
                    y_pred, y_pred_proba = self._train_and_predict_nn(model, X_train, y_train, X_val)
                
                # Calculate metrics
                run_scores['accuracy'].append(accuracy_score(y_val, y_pred))
                run_scores['precision'].append(precision_score(y_val, y_pred, average='binary'))
                run_scores['recall'].append(recall_score(y_val, y_pred, average='binary'))
                run_scores['f1'].append(f1_score(y_val, y_pred, average='binary'))
                
                if len(np.unique(y_val)) > 1:  # Avoid AUC error with single class
                    run_scores['auc'].append(roc_auc_score(y_val, y_pred_proba))
                else:
                    run_scores['auc'].append(0.5)
            
            # Average across folds for this run
            for metric in all_scores.keys():
                all_scores[metric].append(np.mean(run_scores[metric]))
        
        print(f"\\n✅ Cross-validation completed!")
        return all_scores
    
    def _train_and_predict_nn(self, model, X_train, y_train, X_val):
        """
        Train neural network and make predictions
        This is a simplified version - you'd implement your specific training loop
        """
        # Placeholder implementation
        # In practice, you'd implement your specific training procedure here
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = model.to(device)
        
        # Convert to tensors
        X_train_tensor = torch.FloatTensor(X_train).to(device)
        y_train_tensor = torch.LongTensor(y_train).to(device)
        X_val_tensor = torch.FloatTensor(X_val).to(device)
        
        # Simple training loop (you'd use your actual training procedure)
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        criterion = nn.CrossEntropyLoss()
        
        model.train()
        for epoch in range(50):  # Quick training for evaluation
            optimizer.zero_grad()
            outputs = model(X_train_tensor)
            loss = criterion(outputs, y_train_tensor)
            loss.backward()
            optimizer.step()
        
        # Prediction
        model.eval()
        with torch.no_grad():
            outputs = model(X_val_tensor)
            probabilities = torch.softmax(outputs, dim=1)
            predictions = torch.argmax(outputs, dim=1)
        
        return predictions.cpu().numpy(), probabilities[:, 1].cpu().numpy()
    
    def statistical_significance_testing(self, results_dict, alpha=0.05):
        """
        Perform statistical significance testing between methods
        """
        print("📊 Performing statistical significance testing...")
        
        methods = list(results_dict.keys())
        significance_matrix = np.zeros((len(methods), len(methods)))
        p_value_matrix = np.zeros((len(methods), len(methods)))
        
        for i, method1 in enumerate(methods):
            for j, method2 in enumerate(methods):
                if i != j:
                    # Paired t-test
                    scores1 = results_dict[method1]['accuracy']
                    scores2 = results_dict[method2]['accuracy']
                    
                    t_stat, p_value = stats.ttest_rel(scores1, scores2)
                    
                    significance_matrix[i, j] = 1 if p_value < alpha else 0
                    p_value_matrix[i, j] = p_value
        
        # Create significance DataFrame
        significance_df = pd.DataFrame(
            significance_matrix,
            index=methods,
            columns=methods
        )
        
        p_value_df = pd.DataFrame(
            p_value_matrix,
            index=methods,
            columns=methods
        )
        
        self.statistical_tests = {
            'significance_matrix': significance_df,
            'p_value_matrix': p_value_df,
            'alpha': alpha
        }
        
        print(f"✅ Statistical testing completed (α = {alpha})")
        return significance_df, p_value_df
    
    def computational_complexity_analysis(self, models_dict, dataset_sizes=[500, 1000, 2000, 3000]):
        """
        Analyze computational complexity of different methods
        """
        print("⚡ Analyzing computational complexity...")
        
        complexity_results = {}
        
        for model_name, model in models_dict.items():
            print(f"  Testing {model_name}...")
            
            training_times = []
            inference_times = []
            memory_usage = []
            
            for size in dataset_sizes:
                # Generate dummy data of specified size
                X_dummy = np.random.randn(size, 50)  # Adjust dimensions as needed
                y_dummy = np.random.randint(0, 2, size)
                
                # Measure training time and memory
                start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
                
                start_time = time.time()
                
                if hasattr(model, 'fit'):  # Traditional ML
                    model.fit(X_dummy, y_dummy)
                else:  # Neural network
                    # Simplified training for complexity analysis
                    self._quick_train_nn(model, X_dummy, y_dummy)
                
                training_time = time.time() - start_time
                training_times.append(training_time)
                
                # Measure inference time
                start_time = time.time()
                
                if hasattr(model, 'predict'):
                    _ = model.predict(X_dummy[:100])  # Predict on subset
                else:
                    self._quick_predict_nn(model, X_dummy[:100])
                
                inference_time = time.time() - start_time
                inference_times.append(inference_time)
                
                # Measure memory usage
                end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
                memory_usage.append(end_memory - start_memory)
            
            complexity_results[model_name] = {
                'dataset_sizes': dataset_sizes,
                'training_times': training_times,
                'inference_times': inference_times,
                'memory_usage': memory_usage
            }
        
        self.complexity_analysis = complexity_results
        print("✅ Complexity analysis completed!")
        return complexity_results
    
    def _quick_train_nn(self, model, X, y, epochs=10):
        """Quick training for complexity analysis"""
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = model.to(device)
        
        X_tensor = torch.FloatTensor(X).to(device)
        y_tensor = torch.LongTensor(y).to(device)
        
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        criterion = nn.CrossEntropyLoss()
        
        model.train()
        for epoch in range(epochs):
            optimizer.zero_grad()
            outputs = model(X_tensor)
            loss = criterion(outputs, y_tensor)
            loss.backward()
            optimizer.step()
    
    def _quick_predict_nn(self, model, X):
        """Quick prediction for complexity analysis"""
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        X_tensor = torch.FloatTensor(X).to(device)
        
        model.eval()
        with torch.no_grad():
            outputs = model(X_tensor)
            predictions = torch.argmax(outputs, dim=1)
        
        return predictions.cpu().numpy()
    
    def generate_comprehensive_report(self, results_dict, model_descriptions=None):
        """
        Generate comprehensive evaluation report
        """
        print("📋 Generating comprehensive evaluation report...")
        
        report = {
            'summary_statistics': {},
            'statistical_significance': self.statistical_tests,
            'computational_complexity': self.complexity_analysis,
            'recommendations': {}
        }
        
        # Summary statistics
        for method, scores in results_dict.items():
            method_stats = {}
            for metric, values in scores.items():
                method_stats[metric] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'median': np.median(values),
                    'ci_95': np.percentile(values, [2.5, 97.5])
                }
            report['summary_statistics'][method] = method_stats
        
        # Performance ranking
        accuracy_means = {method: np.mean(scores['accuracy']) for method, scores in results_dict.items()}
        ranked_methods = sorted(accuracy_means.items(), key=lambda x: x[1], reverse=True)
        
        report['performance_ranking'] = ranked_methods
        
        # Recommendations
        best_method = ranked_methods[0][0]
        best_accuracy = ranked_methods[0][1]
        
        report['recommendations'] = {
            'best_overall': best_method,
            'best_accuracy': best_accuracy,
            'significant_improvements': self._find_significant_improvements(),
            'computational_efficiency': self._find_most_efficient()
        }
        
        return report
    
    def _find_significant_improvements(self):
        """Find methods with statistically significant improvements"""
        if not self.statistical_tests:
            return []
        
        significance_matrix = self.statistical_tests['significance_matrix']
        improvements = []
        
        for method1 in significance_matrix.index:
            significant_wins = 0
            for method2 in significance_matrix.columns:
                if method1 != method2 and significance_matrix.loc[method1, method2] == 1:
                    significant_wins += 1
            
            if significant_wins > 0:
                improvements.append((method1, significant_wins))
        
        return sorted(improvements, key=lambda x: x[1], reverse=True)
    
    def _find_most_efficient(self):
        """Find most computationally efficient method"""
        if not self.complexity_analysis:
            return None
        
        efficiency_scores = {}
        for method, complexity in self.complexity_analysis.items():
            # Simple efficiency score: inverse of average training time
            avg_training_time = np.mean(complexity['training_times'])
            efficiency_scores[method] = 1 / avg_training_time
        
        most_efficient = max(efficiency_scores.items(), key=lambda x: x[1])
        return most_efficient[0]
    
    def plot_results(self, results_dict, save_path=None):
        """
        Create comprehensive visualization of results
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. Accuracy comparison with error bars
        methods = list(results_dict.keys())
        accuracies = [results_dict[method]['accuracy'] for method in methods]
        
        means = [np.mean(acc) for acc in accuracies]
        stds = [np.std(acc) for acc in accuracies]
        
        axes[0, 0].bar(methods, means, yerr=stds, capsize=5, alpha=0.7)
        axes[0, 0].set_title('Accuracy Comparison with Standard Deviation')
        axes[0, 0].set_ylabel('Accuracy')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 2. Box plot of all metrics
        all_metrics_data = []
        all_metrics_labels = []
        
        for method in methods:
            for metric in ['accuracy', 'precision', 'recall', 'f1']:
                all_metrics_data.extend(results_dict[method][metric])
                all_metrics_labels.extend([f"{method}_{metric}"] * len(results_dict[method][metric]))
        
        df_metrics = pd.DataFrame({
            'score': all_metrics_data,
            'method_metric': all_metrics_labels
        })
        
        # Extract method and metric
        df_metrics['method'] = df_metrics['method_metric'].str.split('_').str[0]
        df_metrics['metric'] = df_metrics['method_metric'].str.split('_').str[1]
        
        sns.boxplot(data=df_metrics, x='method', y='score', hue='metric', ax=axes[0, 1])
        axes[0, 1].set_title('Distribution of All Metrics')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 3. Statistical significance heatmap
        if self.statistical_tests:
            sns.heatmap(
                self.statistical_tests['significance_matrix'],
                annot=True,
                cmap='RdYlBu_r',
                ax=axes[1, 0]
            )
            axes[1, 0].set_title('Statistical Significance Matrix')
        
        # 4. Computational complexity
        if self.complexity_analysis:
            for method, complexity in self.complexity_analysis.items():
                axes[1, 1].plot(
                    complexity['dataset_sizes'],
                    complexity['training_times'],
                    marker='o',
                    label=method
                )
            
            axes[1, 1].set_title('Training Time vs Dataset Size')
            axes[1, 1].set_xlabel('Dataset Size')
            axes[1, 1].set_ylabel('Training Time (seconds)')
            axes[1, 1].legend()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def export_results(self, results_dict, report, filename='evaluation_results.xlsx'):
        """
        Export results to Excel file for detailed analysis
        """
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # Summary statistics
            summary_df = pd.DataFrame()
            for method, stats in report['summary_statistics'].items():
                for metric, values in stats.items():
                    for stat_name, stat_value in values.items():
                        if stat_name != 'ci_95':  # Skip CI for now
                            summary_df.loc[f"{method}_{metric}", stat_name] = stat_value
            
            summary_df.to_excel(writer, sheet_name='Summary_Statistics')
            
            # Statistical significance
            if self.statistical_tests:
                self.statistical_tests['significance_matrix'].to_excel(writer, sheet_name='Significance_Matrix')
                self.statistical_tests['p_value_matrix'].to_excel(writer, sheet_name='P_Values')
            
            # Computational complexity
            if self.complexity_analysis:
                complexity_df = pd.DataFrame()
                for method, complexity in self.complexity_analysis.items():
                    for i, size in enumerate(complexity['dataset_sizes']):
                        complexity_df.loc[f"{method}_{size}", 'training_time'] = complexity['training_times'][i]
                        complexity_df.loc[f"{method}_{size}", 'inference_time'] = complexity['inference_times'][i]
                        complexity_df.loc[f"{method}_{size}", 'memory_usage'] = complexity['memory_usage'][i]
                
                complexity_df.to_excel(writer, sheet_name='Computational_Complexity')
        
        print(f"📊 Results exported to {filename}")

# Usage example
if __name__ == "__main__":
    print("🔬 Comprehensive Evaluation Framework")
    print("Features:")
    print("  ✅ Stratified cross-validation with multiple runs")
    print("  ✅ Statistical significance testing")
    print("  ✅ Computational complexity analysis")
    print("  ✅ Comprehensive reporting and visualization")
    print("  ✅ Excel export for detailed analysis")
