"""
Improved Synthetic Data Generator
Addresses reviewer concerns about data leakage and oversimplified dataset
Maintains focus on three-dimensional behavioral analysis
"""

import numpy as np
import pandas as pd
import torch
from datetime import datetime, timedelta
import random
import networkx as nx
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

class ImprovedSyntheticDataGenerator:
    """
    Generate improved synthetic dataset that prevents data leakage
    Focus: Realistic behavioral changes in compromised accounts
    """
    
    def __init__(self, num_users=3000, num_messages=12000, compromise_rate=0.08):
        self.num_users = num_users
        self.num_messages = num_messages
        self.compromise_rate = compromise_rate
        
        # Shared vocabulary to prevent template-based data leakage
        self.shared_vocabulary = {
            'positive_words': ['great', 'amazing', 'awesome', 'fantastic', 'excellent', 'wonderful'],
            'action_words': ['check', 'try', 'see', 'look', 'discover', 'find', 'explore'],
            'objects': ['thing', 'opportunity', 'deal', 'offer', 'product', 'service', 'solution'],
            'time_words': ['now', 'today', 'soon', 'quickly', 'immediately', 'fast'],
            'social_words': ['friend', 'everyone', 'people', 'community', 'network', 'connection']
        }
        
        # Realistic compromise scenarios
        self.compromise_scenarios = {
            'credential_reuse': {
                'probability': 0.35,
                'behavioral_change_intensity': 'medium',
                'spam_rate': 0.3,
                'detection_difficulty': 'medium'
            },
            'phishing_victim': {
                'probability': 0.25,
                'behavioral_change_intensity': 'low',
                'spam_rate': 0.2,
                'detection_difficulty': 'hard'
            },
            'malware_infection': {
                'probability': 0.25,
                'behavioral_change_intensity': 'high',
                'spam_rate': 0.4,
                'detection_difficulty': 'easy'
            },
            'social_engineering': {
                'probability': 0.15,
                'behavioral_change_intensity': 'very_low',
                'spam_rate': 0.15,
                'detection_difficulty': 'very_hard'
            }
        }
        
    def generate_realistic_users(self):
        """Generate users with realistic vulnerability patterns"""
        users = {}
        
        # User archetypes with realistic distributions
        user_archetypes = {
            'young_casual': {'age_range': (18, 25), 'activity_level': 'medium', 'tech_literacy': 'medium'},
            'adult_professional': {'age_range': (26, 45), 'activity_level': 'low', 'tech_literacy': 'high'},
            'middle_aged': {'age_range': (46, 60), 'activity_level': 'low', 'tech_literacy': 'low'},
            'senior': {'age_range': (61, 75), 'activity_level': 'very_low', 'tech_literacy': 'very_low'},
            'active_user': {'age_range': (20, 50), 'activity_level': 'high', 'tech_literacy': 'medium'},
            'influencer': {'age_range': (22, 40), 'activity_level': 'very_high', 'tech_literacy': 'high'},
            'business': {'age_range': (30, 55), 'activity_level': 'medium', 'tech_literacy': 'medium'}
        }
        
        for i in range(self.num_users):
            user_id = f"user_{i}"
            
            # Select archetype
            archetype_name = np.random.choice(
                list(user_archetypes.keys()),
                p=[0.25, 0.20, 0.15, 0.10, 0.15, 0.05, 0.10]
            )
            archetype = user_archetypes[archetype_name]
            
            # Generate user characteristics
            age = np.random.randint(*archetype['age_range'])
            account_age_days = np.random.randint(30, 2190)  # 1 month to 6 years
            
            # Activity level mapping
            activity_mapping = {
                'very_low': np.random.randint(1, 3),
                'low': np.random.randint(2, 6),
                'medium': np.random.randint(5, 12),
                'high': np.random.randint(10, 20),
                'very_high': np.random.randint(18, 35)
            }
            base_activity = activity_mapping[archetype['activity_level']]
            
            # Tech literacy mapping
            tech_literacy_mapping = {
                'very_low': np.random.uniform(0.1, 0.3),
                'low': np.random.uniform(0.2, 0.5),
                'medium': np.random.uniform(0.4, 0.7),
                'high': np.random.uniform(0.6, 0.9)
            }
            tech_literacy = tech_literacy_mapping[archetype['tech_literacy']]
            
            # Calculate vulnerability based on multiple realistic factors
            vulnerability_score = self._calculate_vulnerability(age, tech_literacy, account_age_days, archetype_name)
            
            # Determine compromise status
            is_compromised = vulnerability_score > (1 - self.compromise_rate * 2.5)
            
            # Select compromise scenario if compromised
            compromise_scenario = None
            compromise_date = None
            if is_compromised:
                compromise_scenario = np.random.choice(
                    list(self.compromise_scenarios.keys()),
                    p=[0.35, 0.25, 0.25, 0.15]
                )
                compromise_date = datetime.now() - timedelta(days=np.random.randint(1, 120))
            
            # Generate weekly activity pattern
            weekly_pattern = self._generate_activity_pattern(base_activity, archetype_name)
            
            # Connection characteristics
            connection_range = self._get_connection_range(archetype_name)
            num_connections = np.random.randint(*connection_range)
            
            users[user_id] = {
                'user_id': user_id,
                'archetype': archetype_name,
                'age': age,
                'account_age_days': account_age_days,
                'base_activity': base_activity,
                'tech_literacy': tech_literacy,
                'weekly_pattern': weekly_pattern,
                'num_connections': num_connections,
                'vulnerability_score': vulnerability_score,
                'is_compromised': is_compromised,
                'compromise_scenario': compromise_scenario,
                'compromise_date': compromise_date,
                'connections': set()
            }
        
        return users
    
    def _calculate_vulnerability(self, age, tech_literacy, account_age_days, archetype):
        """Calculate realistic vulnerability score"""
        vulnerability = 0.0
        
        # Age-based vulnerability (U-shaped curve)
        if age < 25 or age > 60:
            vulnerability += 0.2
        elif 25 <= age <= 35 or 50 <= age <= 60:
            vulnerability += 0.1
        
        # Tech literacy (inverse relationship)
        vulnerability += (1 - tech_literacy) * 0.3
        
        # Account age (newer accounts more vulnerable)
        if account_age_days < 90:
            vulnerability += 0.2
        elif account_age_days < 365:
            vulnerability += 0.1
        
        # Archetype-specific factors
        if archetype in ['senior', 'middle_aged']:
            vulnerability += 0.15
        elif archetype in ['influencer', 'business']:
            vulnerability += 0.1  # High-value targets
        
        # Random factor to prevent perfect prediction
        vulnerability += np.random.uniform(-0.15, 0.15)
        
        return np.clip(vulnerability, 0, 1)
    
    def _generate_activity_pattern(self, base_activity, archetype):
        """Generate realistic weekly activity patterns"""
        pattern = []
        
        for day in range(7):  # Monday to Sunday
            if archetype == 'business':
                # Business accounts more active on weekdays
                if day < 5:  # Weekdays
                    day_activity = int(base_activity * np.random.uniform(1.0, 1.4))
                else:  # Weekends
                    day_activity = int(base_activity * np.random.uniform(0.3, 0.7))
            elif archetype in ['young_casual', 'active_user']:
                # Young users more active on weekends
                if day < 5:  # Weekdays
                    day_activity = int(base_activity * np.random.uniform(0.7, 1.1))
                else:  # Weekends
                    day_activity = int(base_activity * np.random.uniform(1.2, 1.8))
            else:
                # Regular pattern with some variance
                day_activity = int(base_activity * np.random.uniform(0.8, 1.2))
            
            pattern.append(max(0, day_activity))
        
        return pattern
    
    def _get_connection_range(self, archetype):
        """Get realistic connection ranges by archetype"""
        ranges = {
            'young_casual': (50, 300),
            'adult_professional': (100, 500),
            'middle_aged': (30, 200),
            'senior': (20, 100),
            'active_user': (200, 800),
            'influencer': (1000, 10000),
            'business': (300, 3000)
        }
        return ranges.get(archetype, (50, 300))
    
    def generate_social_network(self, users):
        """Generate realistic social network with homophily"""
        G = nx.Graph()
        user_list = list(users.keys())
        G.add_nodes_from(user_list)
        
        for user_id, user_data in users.items():
            target_connections = user_data['num_connections']
            user_age = user_data['age']
            user_archetype = user_data['archetype']
            
            # Calculate connection probabilities
            connection_probs = []
            for other_id, other_data in users.items():
                if other_id == user_id:
                    continue
                
                # Age homophily
                age_similarity = 1 - abs(user_age - other_data['age']) / 50
                
                # Archetype compatibility
                archetype_compatibility = self._calculate_archetype_compatibility(user_archetype, other_data['archetype'])
                
                # Activity level compatibility
                activity_compatibility = 1 - abs(user_data['base_activity'] - other_data['base_activity']) / 30
                
                # Combined probability
                prob = (age_similarity + archetype_compatibility + activity_compatibility) / 3
                connection_probs.append((other_id, prob))
            
            # Select connections based on probabilities
            connection_probs.sort(key=lambda x: x[1] + np.random.uniform(0, 0.2), reverse=True)
            
            actual_connections = min(target_connections, len(connection_probs))
            selected_connections = connection_probs[:actual_connections]
            
            # Add edges
            for connected_user, prob in selected_connections:
                if not G.has_edge(user_id, connected_user):
                    weight = max(1, int(prob * 10))
                    G.add_edge(user_id, connected_user, weight=weight)
                    users[user_id]['connections'].add(connected_user)
                    users[connected_user]['connections'].add(user_id)
        
        return G
    
    def _calculate_archetype_compatibility(self, arch1, arch2):
        """Calculate compatibility between user archetypes"""
        # Define compatibility matrix
        compatibility = {
            ('young_casual', 'young_casual'): 0.9,
            ('young_casual', 'active_user'): 0.7,
            ('adult_professional', 'adult_professional'): 0.8,
            ('adult_professional', 'business'): 0.6,
            ('influencer', 'young_casual'): 0.6,
            ('influencer', 'active_user'): 0.8,
            ('business', 'adult_professional'): 0.7,
            ('senior', 'middle_aged'): 0.7,
        }
        
        # Check both directions
        key1 = (arch1, arch2)
        key2 = (arch2, arch1)
        
        if key1 in compatibility:
            return compatibility[key1]
        elif key2 in compatibility:
            return compatibility[key2]
        else:
            return 0.3  # Default low compatibility
    
    def generate_realistic_messages(self, users, network_graph):
        """Generate messages with realistic content using shared vocabulary"""
        messages = []
        
        for i in range(self.num_messages):
            # Select sender based on activity level
            sender_weights = [sum(users[uid]['weekly_pattern']) for uid in users.keys()]
            sender_weights = np.array(sender_weights) / sum(sender_weights)
            
            sender_idx = np.random.choice(len(users), p=sender_weights)
            sender_id = f"user_{sender_idx}"
            sender = users[sender_id]
            
            # Select recipient from connections
            if not sender['connections']:
                continue
            
            recipient_id = np.random.choice(list(sender['connections']))
            
            # Determine if spam based on compromise status and scenario
            is_spam = self._determine_spam_status(sender)
            
            # Generate content using shared vocabulary
            content = self._generate_realistic_content(sender, is_spam)
            
            # Generate timestamp with realistic patterns
            timestamp = self._generate_realistic_timestamp(sender, is_spam)
            
            message = {
                'message_id': f"msg_{i}",
                'sender_id': sender_id,
                'recipient_id': recipient_id,
                'content': content,
                'timestamp': timestamp,
                'is_spam': is_spam,
                'sender_compromised': sender['is_compromised'],
                'compromise_scenario': sender['compromise_scenario']
            }
            
            messages.append(message)
        
        return messages
    
    def _determine_spam_status(self, sender):
        """Determine spam status based on realistic patterns"""
        if not sender['is_compromised']:
            # Legitimate users: very low spam rate
            if sender['archetype'] in ['business', 'influencer']:
                return np.random.random() < 0.03  # 3% promotional content
            else:
                return np.random.random() < 0.005  # 0.5% false positives
        
        # Compromised users: scenario-based spam rates
        scenario = self.compromise_scenarios[sender['compromise_scenario']]
        base_spam_rate = scenario['spam_rate']
        
        # Adjust based on time since compromise
        if sender['compromise_date']:
            days_since_compromise = (datetime.now() - sender['compromise_date']).days
            if days_since_compromise < 7:  # First week - higher activity
                spam_rate = base_spam_rate * 1.3
            elif days_since_compromise < 30:  # First month
                spam_rate = base_spam_rate * 1.1
            else:
                spam_rate = base_spam_rate * 0.9
        else:
            spam_rate = base_spam_rate
        
        return np.random.random() < spam_rate
    
    def _generate_realistic_content(self, sender, is_spam):
        """Generate content using shared vocabulary to prevent data leakage"""
        positive = np.random.choice(self.shared_vocabulary['positive_words'])
        action = np.random.choice(self.shared_vocabulary['action_words'])
        obj = np.random.choice(self.shared_vocabulary['objects'])
        time_word = np.random.choice(self.shared_vocabulary['time_words'])
        social = np.random.choice(self.shared_vocabulary['social_words'])
        
        if is_spam:
            # Spam uses more persuasive combinations but same vocabulary
            templates = [
                f"Hey {social}! This {obj} is {positive}. You should {action} it {time_word}!",
                f"Found a {positive} {obj} that everyone should {action}. Don't wait!",
                f"This {obj} is {positive}! {action.capitalize()} it out {time_word}.",
                f"Amazing {obj} I had to share with my {social}. {action.capitalize()} this!"
            ]
            
            # Add subtle spam indicators (not always)
            content = np.random.choice(templates)
            if np.random.random() < 0.3:  # 30% chance
                content += " Message me for details."
            
        else:
            # Legitimate content uses same vocabulary but different patterns
            templates = [
                f"Had a {positive} day! Hope you're doing well.",
                f"Thanks for being such a {positive} {social}!",
                f"Just wanted to {action} in and say hello.",
                f"Hope you have a {positive} day {time_word}!",
                f"This {obj} reminded me of you. Thought you'd like it.",
                f"Great to connect with {positive} people like you."
            ]
            content = np.random.choice(templates)
        
        return content
    
    def _generate_realistic_timestamp(self, sender, is_spam):
        """Generate realistic timestamps"""
        base_time = datetime.now() - timedelta(hours=np.random.randint(0, 168))
        
        if sender['is_compromised'] and is_spam:
            scenario = sender['compromise_scenario']
            
            if scenario == 'malware_infection':
                # Malware might post at unusual times
                hour_offset = np.random.normal(0, 6)
                base_time += timedelta(hours=hour_offset)
            elif scenario == 'credential_reuse':
                # Might show burst patterns
                if np.random.random() < 0.4:  # 40% chance of burst
                    burst_offset = np.random.randint(-2, 2)
                    base_time += timedelta(hours=burst_offset)
        
        return base_time
    
    def generate_complete_dataset(self):
        """Generate complete improved synthetic dataset"""
        print(f"🔬 Generating improved synthetic dataset with {self.num_users} users...")
        print("🎯 Focus: Preventing data leakage while maintaining realistic behavioral patterns")
        
        # Generate users
        users = self.generate_realistic_users()
        compromised_count = sum(1 for u in users.values() if u['is_compromised'])
        
        print(f"  📊 Generated {len(users)} users ({compromised_count} compromised, {compromised_count/len(users)*100:.1f}%)")
        
        # Generate social network
        network_graph = self.generate_social_network(users)
        print(f"  🌐 Generated network with {network_graph.number_of_edges()} connections")
        
        # Generate messages
        messages = self.generate_realistic_messages(users, network_graph)
        spam_count = sum(1 for m in messages if m['is_spam'])
        
        print(f"  💬 Generated {len(messages)} messages ({spam_count} spam, {spam_count/len(messages)*100:.1f}%)")
        
        # Calculate scenario distribution
        scenario_dist = {}
        for user in users.values():
            if user['compromise_scenario']:
                scenario_dist[user['compromise_scenario']] = scenario_dist.get(user['compromise_scenario'], 0) + 1
        
        print(f"  🎯 Compromise scenarios: {scenario_dist}")
        
        return {
            'users': users,
            'messages': messages,
            'network_graph': network_graph,
            'statistics': {
                'num_users': len(users),
                'num_compromised': compromised_count,
                'compromise_rate': compromised_count / len(users),
                'num_messages': len(messages),
                'num_spam': spam_count,
                'spam_rate': spam_count / len(messages),
                'num_connections': network_graph.number_of_edges(),
                'compromise_scenarios': scenario_dist
            }
        }

# Usage example
if __name__ == "__main__":
    generator = ImprovedSyntheticDataGenerator(num_users=3000, num_messages=12000)
    dataset = generator.generate_complete_dataset()
    
    print("\\n✅ Improved synthetic dataset generated!")
    print("   - Shared vocabulary prevents template-based data leakage")
    print("   - Realistic vulnerability patterns")
    print("   - Appropriate challenge level for advanced methods")
