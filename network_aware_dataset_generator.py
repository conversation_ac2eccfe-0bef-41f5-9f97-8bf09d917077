"""
Network-Aware Dataset Generator
Properly models message recipients and interaction patterns
"""

import numpy as np
import pandas as pd
import torch
from datetime import datetime, timedelta
import random
import networkx as nx
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

class NetworkAwareDatasetGenerator:
    """
    Generate realistic social network data with proper sender-recipient relationships
    """
    
    def __init__(self, num_users=3000, num_messages=12000, compromise_rate=0.08):
        self.num_users = num_users
        self.num_messages = num_messages
        self.compromise_rate = compromise_rate
        
        # Message types with different recipient patterns
        self.message_types = {
            'public_post': {
                'probability': 0.4,
                'recipients': 'followers',  # All followers see it
                'spam_likelihood': 0.3
            },
            'direct_message': {
                'probability': 0.25,
                'recipients': 'single',     # One specific recipient
                'spam_likelihood': 0.6      # DMs more likely to be spam
            },
            'reply': {
                'probability': 0.2,
                'recipients': 'thread',     # Reply to specific message
                'spam_likelihood': 0.1
            },
            'group_message': {
                'probability': 0.1,
                'recipients': 'multiple',   # Multiple specific recipients
                'spam_likelihood': 0.4
            },
            'broadcast': {
                'probability': 0.05,
                'recipients': 'all_connections',  # All connections
                'spam_likelihood': 0.8      # Broadcasts often spam
            }
        }
        
        # Attack targeting patterns
        self.attack_targeting = {
            'credential_stuffing': {
                'target_preference': 'random',
                'message_types': ['direct_message', 'public_post'],
                'volume_pattern': 'moderate'
            },
            'phishing_victim': {
                'target_preference': 'similar_users',  # Target similar demographics
                'message_types': ['direct_message', 'group_message'],
                'volume_pattern': 'low'
            },
            'malware_infection': {
                'target_preference': 'all_connections',  # Spam everyone
                'message_types': ['broadcast', 'direct_message'],
                'volume_pattern': 'high'
            },
            'social_engineering': {
                'target_preference': 'high_value_targets',  # Target influencers/business
                'message_types': ['direct_message', 'reply'],
                'volume_pattern': 'very_low'
            }
        }
    
    def generate_users_with_network(self):
        """Generate users and their social network simultaneously"""
        users = {}
        
        # User archetypes with network characteristics
        user_archetypes = {
            'casual_young': {
                'age_range': (18, 25), 
                'activity_base': 8, 
                'tech_savvy': 0.7,
                'connection_preference': 'peers',
                'follower_ratio': 1.2  # Slightly more followers than following
            },
            'casual_adult': {
                'age_range': (26, 45), 
                'activity_base': 5, 
                'tech_savvy': 0.5,
                'connection_preference': 'family_friends',
                'follower_ratio': 0.8
            },
            'professional': {
                'age_range': (25, 55), 
                'activity_base': 6, 
                'tech_savvy': 0.8,
                'connection_preference': 'professional',
                'follower_ratio': 1.5
            },
            'influencer': {
                'age_range': (20, 40), 
                'activity_base': 20, 
                'tech_savvy': 0.9,
                'connection_preference': 'broad',
                'follower_ratio': 10.0  # Many more followers
            },
            'business': {
                'age_range': (30, 60), 
                'activity_base': 10, 
                'tech_savvy': 0.6,
                'connection_preference': 'customers',
                'follower_ratio': 3.0
            }
        }
        
        # Generate users first
        for i in range(self.num_users):\n            user_id = f\"user_{i}\"\n            \n            # Assign archetype\n            archetype_name = np.random.choice(\n                list(user_archetypes.keys()),\n                p=[0.3, 0.25, 0.2, 0.05, 0.2]  # Realistic distribution\n            )\n            archetype = user_archetypes[archetype_name]\n            \n            # Generate user attributes\n            age = np.random.randint(*archetype['age_range'])\n            tech_savvy = np.clip(archetype['tech_savvy'] + np.random.normal(0, 0.1), 0, 1)\n            \n            # Account age\n            account_age_days = np.random.randint(30, 1095)\n            \n            # Follower counts based on archetype\n            if archetype_name == 'influencer':\n                base_followers = np.random.randint(5000, 100000)\n            elif archetype_name == 'business':\n                base_followers = np.random.randint(500, 20000)\n            elif archetype_name == 'professional':\n                base_followers = np.random.randint(200, 3000)\n            else:\n                base_followers = np.random.randint(50, 1500)\n            \n            following_count = int(base_followers / archetype['follower_ratio'])\n            following_count = max(20, following_count)  # Minimum following\n            \n            # Calculate vulnerability\n            vulnerability_score = self._calculate_vulnerability(age, tech_savvy, account_age_days, archetype_name)\n            is_compromised = vulnerability_score > (1 - self.compromise_rate * 2)\n            \n            # Attack pattern if compromised\n            attack_pattern = None\n            if is_compromised:\n                attack_pattern = np.random.choice(\n                    list(self.attack_targeting.keys()),\n                    p=[0.4, 0.3, 0.2, 0.1]\n                )\n            \n            users[user_id] = {\n                'user_id': user_id,\n                'archetype': archetype_name,\n                'age': age,\n                'tech_savvy': tech_savvy,\n                'account_age_days': account_age_days,\n                'follower_count': base_followers,\n                'following_count': following_count,\n                'vulnerability_score': vulnerability_score,\n                'is_compromised': is_compromised,\n                'attack_pattern': attack_pattern,\n                'connection_preference': archetype['connection_preference'],\n                'followers': set(),  # Will be populated when creating network\n                'following': set(),\n                'connections': set()\n            }\n        \n        # Generate social network\n        network_graph = self._generate_social_network(users)\n        \n        return users, network_graph\n    \n    def _calculate_vulnerability(self, age, tech_savvy, account_age_days, archetype):\n        \"\"\"Calculate user vulnerability to compromise\"\"\"\n        vulnerability = 0.0\n        \n        # Age factor (U-shaped: young and old more vulnerable)\n        if age > 55:\n            vulnerability += 0.25\n        elif age < 22:\n            vulnerability += 0.20\n        \n        # Tech savviness\n        vulnerability += (1 - tech_savvy) * 0.3\n        \n        # Account age (newer accounts more vulnerable)\n        if account_age_days < 90:\n            vulnerability += 0.15\n        elif account_age_days < 365:\n            vulnerability += 0.05\n        \n        # Archetype factor\n        if archetype in ['influencer', 'business']:\n            vulnerability += 0.10  # High-value targets\n        \n        # Random factor\n        vulnerability += np.random.uniform(-0.15, 0.15)\n        \n        return np.clip(vulnerability, 0, 1)\n    \n    def _generate_social_network(self, users):\n        \"\"\"Generate realistic social network connections\"\"\"\n        G = nx.DiGraph()  # Directed graph (following relationships)\n        user_list = list(users.keys())\n        G.add_nodes_from(user_list)\n        \n        for user_id, user_data in users.items():\n            following_count = user_data['following_count']\n            connection_preference = user_data['connection_preference']\n            age = user_data['age']\n            archetype = user_data['archetype']\n            \n            # Select users to follow based on preference\n            potential_follows = []\n            \n            for other_id, other_data in users.items():\n                if other_id == user_id:\n                    continue\n                \n                # Calculate connection probability\n                prob = self._calculate_connection_probability(\n                    user_data, other_data, connection_preference\n                )\n                potential_follows.append((other_id, prob))\n            \n            # Sort by probability and add randomness\n            potential_follows.sort(key=lambda x: x[1] + np.random.uniform(0, 0.2), reverse=True)\n            \n            # Select top connections\n            num_to_follow = min(following_count, len(potential_follows))\n            selected_follows = potential_follows[:num_to_follow]\n            \n            # Add edges\n            for followed_user, prob in selected_follows:\n                G.add_edge(user_id, followed_user, weight=prob)\n                users[user_id]['following'].add(followed_user)\n                users[followed_user]['followers'].add(user_id)\n                \n                # Mutual connections\n                users[user_id]['connections'].add(followed_user)\n                users[followed_user]['connections'].add(user_id)\n        \n        return G\n    \n    def _calculate_connection_probability(self, user1, user2, preference):\n        \"\"\"Calculate probability of user1 following user2\"\"\"\n        base_prob = 0.1\n        \n        age_diff = abs(user1['age'] - user2['age'])\n        archetype1 = user1['archetype']\n        archetype2 = user2['archetype']\n        \n        if preference == 'peers':\n            # Similar age and archetype\n            if age_diff < 5:\n                base_prob += 0.3\n            if archetype1 == archetype2:\n                base_prob += 0.2\n                \n        elif preference == 'family_friends':\n            # Broader age range, similar archetypes\n            if age_diff < 15:\n                base_prob += 0.2\n            if archetype1 == archetype2:\n                base_prob += 0.3\n                \n        elif preference == 'professional':\n            # Professional connections\n            if archetype2 in ['professional', 'business']:\n                base_prob += 0.4\n            if age_diff < 20:\n                base_prob += 0.1\n                \n        elif preference == 'broad':\n            # Influencers connect broadly\n            base_prob += 0.2\n            if archetype2 == 'influencer':\n                base_prob += 0.3\n                \n        elif preference == 'customers':\n            # Business accounts\n            if archetype2 != 'business':  # Don't follow other businesses much\n                base_prob += 0.2\n        \n        return min(base_prob, 1.0)\n    \n    def generate_messages_with_recipients(self, users, network_graph):\n        \"\"\"Generate messages with proper sender-recipient relationships\"\"\"\n        messages = []\n        message_threads = defaultdict(list)  # Track conversation threads\n        \n        for i in range(self.num_messages):\n            # Select sender based on activity level\n            sender_weights = [users[uid]['follower_count'] + users[uid]['following_count'] \n                            for uid in users.keys()]\n            sender_weights = np.array(sender_weights) / sum(sender_weights)\n            \n            sender_idx = np.random.choice(len(users), p=sender_weights)\n            sender_id = f\"user_{sender_idx}\"\n            sender = users[sender_id]\n            \n            # Determine message type\n            message_type = np.random.choice(\n                list(self.message_types.keys()),\n                p=[self.message_types[mt]['probability'] for mt in self.message_types.keys()]\n            )\n            \n            # Determine if spam based on user status and message type\n            is_spam = self._determine_spam_status(sender, message_type)\n            \n            # Generate recipients based on message type\n            recipients = self._generate_recipients(sender_id, message_type, users, network_graph, message_threads)\n            \n            # Generate content\n            content = self._generate_message_content(is_spam, message_type, sender, recipients)\n            \n            # Generate timestamp\n            timestamp = self._generate_timestamp(sender, is_spam)\n            \n            message = {\n                'message_id': f\"msg_{i}\",\n                'sender_id': sender_id,\n                'recipients': recipients,  # ✅ NOW WE HAVE RECIPIENTS!\n                'message_type': message_type,\n                'content': content,\n                'timestamp': timestamp,\n                'is_spam': is_spam,\n                'sender_compromised': sender['is_compromised'],\n                'attack_pattern': sender['attack_pattern'] if sender['is_compromised'] else None\n            }\n            \n            messages.append(message)\n            \n            # Update message threads for replies\n            if message_type == 'reply' and recipients:\n                thread_key = tuple(sorted([sender_id] + recipients))\n                message_threads[thread_key].append(message)\n        \n        return messages\n    \n    def _determine_spam_status(self, sender, message_type):\n        \"\"\"Determine if message is spam based on sender and message type\"\"\"\n        base_spam_prob = self.message_types[message_type]['spam_likelihood']\n        \n        if sender['is_compromised']:\n            attack_pattern = sender['attack_pattern']\n            \n            # Different attack patterns have different spam rates\n            if attack_pattern == 'credential_stuffing':\n                spam_multiplier = 1.5\n            elif attack_pattern == 'phishing_victim':\n                spam_multiplier = 2.0\n            elif attack_pattern == 'malware_infection':\n                spam_multiplier = 3.0\n            elif attack_pattern == 'social_engineering':\n                spam_multiplier = 0.8  # More sophisticated, less obvious spam\n            else:\n                spam_multiplier = 1.5\n                \n            final_spam_prob = base_spam_prob * spam_multiplier\n        else:\n            # Legitimate users can occasionally post promotional content\n            if sender['archetype'] in ['business', 'influencer']:\n                final_spam_prob = base_spam_prob * 0.3\n            else:\n                final_spam_prob = base_spam_prob * 0.1\n        \n        return np.random.random() < min(final_spam_prob, 0.8)  # Cap at 80%\n    \n    def _generate_recipients(self, sender_id, message_type, users, network_graph, message_threads):\n        \"\"\"Generate appropriate recipients based on message type\"\"\"\n        sender = users[sender_id]\n        \n        if message_type == 'public_post':\n            # All followers can see public posts\n            return list(sender['followers'])\n            \n        elif message_type == 'direct_message':\n            # Single recipient from connections\n            if sender['connections']:\n                return [np.random.choice(list(sender['connections']))]\n            else:\n                return []\n                \n        elif message_type == 'reply':\n            # Reply to someone who messaged recently\n            recent_senders = []\n            for thread in message_threads.values():\n                for msg in thread[-3:]:  # Last 3 messages\n                    if sender_id in msg['recipients']:\n                        recent_senders.append(msg['sender_id'])\n            \n            if recent_senders:\n                return [np.random.choice(recent_senders)]\n            elif sender['connections']:\n                return [np.random.choice(list(sender['connections']))]\n            else:\n                return []\n                \n        elif message_type == 'group_message':\n            # Multiple recipients from connections\n            if len(sender['connections']) >= 2:\n                num_recipients = min(np.random.randint(2, 6), len(sender['connections']))\n                return list(np.random.choice(list(sender['connections']), \n                                           size=num_recipients, replace=False))\n            else:\n                return list(sender['connections'])\n                \n        elif message_type == 'broadcast':\n            # All connections\n            return list(sender['connections'])\n        \n        return []\n    \n    def _generate_message_content(self, is_spam, message_type, sender, recipients):\n        \"\"\"Generate message content based on context\"\"\"\n        # Simplified content generation for now\n        if is_spam:\n            templates = [\n                \"Check out this amazing opportunity!\",\n                \"You won't believe this deal!\",\n                \"Limited time offer - act now!\",\n                \"This changed my life, try it!\"\n            ]\n        else:\n            templates = [\n                \"Hope you're having a great day!\",\n                \"Thanks for connecting!\",\n                \"Interesting article I thought you'd like.\",\n                \"Looking forward to our meeting.\"\n            ]\n        \n        base_content = np.random.choice(templates)\n        \n        # Modify based on message type\n        if message_type == 'direct_message' and recipients:\n            base_content = f\"Hi {recipients[0]}, {base_content.lower()}\"\n        elif message_type == 'group_message':\n            base_content = f\"Hi everyone, {base_content.lower()}\"\n        elif message_type == 'reply':\n            base_content = f\"Thanks for your message! {base_content}\"\n        \n        return base_content\n    \n    def _generate_timestamp(self, sender, is_spam):\n        \"\"\"Generate realistic timestamp\"\"\"\n        base_time = datetime.now() - timedelta(hours=np.random.randint(0, 168))\n        \n        if sender['is_compromised'] and is_spam:\n            # Compromised accounts might post at unusual times\n            if sender['attack_pattern'] == 'malware_infection':\n                hour_offset = np.random.normal(0, 6)  # High variance\n            else:\n                hour_offset = np.random.normal(0, 3)  # Medium variance\n            base_time += timedelta(hours=hour_offset)\n        \n        return base_time\n    \n    def generate_complete_dataset(self):\n        \"\"\"Generate complete dataset with proper sender-recipient relationships\"\"\"\n        print(f\"🔬 Generating network-aware dataset with {self.num_users} users...\")\n        \n        # Generate users and network\n        users, network_graph = self.generate_users_with_network()\n        compromised_count = sum(1 for u in users.values() if u['is_compromised'])\n        print(f\"  📊 Generated {len(users)} users ({compromised_count} compromised)\")\n        print(f\"  🌐 Generated network with {network_graph.number_of_edges()} connections\")\n        \n        # Generate messages with recipients\n        messages = self.generate_messages_with_recipients(users, network_graph)\n        spam_count = sum(1 for m in messages if m['is_spam'])\n        print(f\"  💬 Generated {len(messages)} messages ({spam_count} spam)\")\n        \n        # Calculate message type distribution\n        message_type_dist = {}\n        for msg in messages:\n            msg_type = msg['message_type']\n            message_type_dist[msg_type] = message_type_dist.get(msg_type, 0) + 1\n        \n        print(f\"  📨 Message type distribution: {message_type_dist}\")\n        \n        return {\n            'users': users,\n            'messages': messages,\n            'network_graph': network_graph,\n            'statistics': {\n                'num_users': len(users),\n                'num_compromised': compromised_count,\n                'compromise_rate': compromised_count / len(users),\n                'num_messages': len(messages),\n                'num_spam': spam_count,\n                'spam_rate': spam_count / len(messages),\n                'num_connections': network_graph.number_of_edges(),\n                'message_types': message_type_dist\n            }\n        }\n\n# Usage example\nif __name__ == \"__main__\":\n    generator = NetworkAwareDatasetGenerator(num_users=1000, num_messages=4000)\n    dataset = generator.generate_complete_dataset()\n    \n    # Show example messages with recipients\n    print(\"\\n📨 Example Messages:\")\n    for i, msg in enumerate(dataset['messages'][:5]):\n        print(f\"  {i+1}. {msg['sender_id']} → {msg['recipients']} ({msg['message_type']})\")\n        print(f\"     Content: {msg['content']}\")\n        print(f\"     Spam: {msg['is_spam']}\")
