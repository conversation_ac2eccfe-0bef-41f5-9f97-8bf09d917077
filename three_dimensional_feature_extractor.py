"""
Three-Dimensional Feature Extractor
Maintains original approach: Content + Structural + Temporal behavioral analysis
Focus: Detecting behavioral changes in compromised accounts targeting existing connections
"""

import numpy as np
import pandas as pd
import torch
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import networkx as nx
from sklearn.preprocessing import StandardScaler
from transformers import DistilBertTokenizer, DistilBertModel
import warnings
warnings.filterwarnings('ignore')

class ThreeDimensionalFeatureExtractor:
    """
    Extract features in three behavioral dimensions:
    1. Content Behavior: What they say and how they say it
    2. Structural Behavior: Who they message and connection patterns  
    3. Temporal Behavior: When and how frequently they message
    
    Focus: Detecting behavioral changes when accounts are compromised
    """
    
    def __init__(self):
        # Initialize BERT for content analysis
        self.tokenizer = DistilBertTokenizer.from_pretrained('distilbert-base-uncased')
        self.bert_model = DistilBertModel.from_pretrained('distilbert-base-uncased')
        self.bert_model.eval()
        
        # Content analysis keywords
        self.promotional_keywords = [
            'opportunity', 'deal', 'offer', 'limited', 'urgent', 'free', 'discount',
            'money', 'earn', 'profit', 'business', 'investment', 'click', 'buy'
        ]
        
        self.persuasion_indicators = [
            'amazing', 'incredible', 'unbelievable', 'guaranteed', 'proven',
            'secret', 'exclusive', 'special', 'limited time', 'act now'
        ]
    
    def extract_comprehensive_features(self, dataset):
        """Extract three-dimensional behavioral features"""
        users = dataset['users']
        messages = dataset['messages']
        network_graph = dataset['network_graph']
        
        print("🔍 Extracting three-dimensional behavioral features...")
        print("   📝 Content Behavior | 🌐 Structural Behavior | ⏰ Temporal Behavior")
        
        user_features = {}
        
        for user_id, user_data in users.items():
            print(f"  Processing {user_id}...", end='\\r')
            
            # Get user's messages
            user_messages = [msg for msg in messages if msg['sender_id'] == user_id]
            
            # Extract three-dimensional features
            content_features = self._extract_content_behavior(user_messages)
            structural_features = self._extract_structural_behavior(user_id, user_messages, users, network_graph)
            temporal_features = self._extract_temporal_behavior(user_messages, user_data)
            
            user_features[user_id] = {
                'content_features': content_features,
                'structural_features': structural_features,
                'temporal_features': temporal_features,
                'label': user_data['is_compromised']
            }
        
        print("\\n✅ Three-dimensional feature extraction completed!")
        return user_features
    
    def _extract_content_behavior(self, user_messages):
        """Extract content behavioral features"""
        features = {}
        
        if not user_messages:
            # Return zero features for users with no messages
            return {f'content_{i}': 0.0 for i in range(25)}
        
        # Basic content statistics
        all_content = [msg['content'] for msg in user_messages]
        spam_messages = [msg for msg in user_messages if msg['is_spam']]
        
        features['spam_ratio'] = len(spam_messages) / len(user_messages)
        
        # Content length analysis
        message_lengths = [len(content.split()) for content in all_content]
        features['avg_message_length'] = np.mean(message_lengths)
        features['message_length_variance'] = np.var(message_lengths)
        
        # Keyword analysis
        promotional_count = 0
        persuasion_count = 0
        
        for content in all_content:
            content_lower = content.lower()
            promotional_count += sum(1 for keyword in self.promotional_keywords if keyword in content_lower)
            persuasion_count += sum(1 for indicator in self.persuasion_indicators if indicator in content_lower)
        
        features['promotional_keywords_per_message'] = promotional_count / len(all_content)
        features['persuasion_indicators_per_message'] = persuasion_count / len(all_content)
        
        # Content diversity
        all_words = []
        for content in all_content:
            all_words.extend(content.lower().split())
        
        unique_words = set(all_words)
        features['vocabulary_diversity'] = len(unique_words) / max(len(all_words), 1)
        
        # Repetition analysis (compromised accounts might repeat similar messages)
        content_similarity = []
        for i in range(len(all_content)):
            for j in range(i+1, len(all_content)):
                words1 = set(all_content[i].lower().split())
                words2 = set(all_content[j].lower().split())
                if len(words1) > 0 and len(words2) > 0:
                    similarity = len(words1.intersection(words2)) / len(words1.union(words2))
                    content_similarity.append(similarity)
        
        features['avg_content_similarity'] = np.mean(content_similarity) if content_similarity else 0
        
        # Spam vs non-spam content differences
        if spam_messages and len(spam_messages) < len(user_messages):
            spam_content = [msg['content'] for msg in spam_messages]
            non_spam_content = [msg['content'] for msg in user_messages if not msg['is_spam']]
            
            spam_lengths = [len(content.split()) for content in spam_content]
            non_spam_lengths = [len(content.split()) for content in non_spam_content]
            
            features['spam_length_difference'] = np.mean(spam_lengths) - np.mean(non_spam_lengths)
            
            # Promotional keyword difference
            spam_promo = sum(sum(1 for keyword in self.promotional_keywords if keyword in content.lower()) 
                           for content in spam_content) / len(spam_content)
            non_spam_promo = sum(sum(1 for keyword in self.promotional_keywords if keyword in content.lower()) 
                               for content in non_spam_content) / len(non_spam_content)
            features['spam_promotional_difference'] = spam_promo - non_spam_promo
        else:
            features['spam_length_difference'] = 0
            features['spam_promotional_difference'] = 0
        
        # BERT embeddings for semantic analysis
        if len(all_content) > 0:
            # Sample messages for BERT analysis (computational efficiency)
            sample_size = min(5, len(all_content))
            sample_content = np.random.choice(all_content, sample_size, replace=False)
            
            embeddings = []
            for content in sample_content:
                inputs = self.tokenizer(content, return_tensors='pt', truncation=True, max_length=128)
                with torch.no_grad():
                    outputs = self.bert_model(**inputs)
                    embedding = outputs.last_hidden_state[0, 0, :].numpy()
                    embeddings.append(embedding)
            
            # Use first 15 dimensions of average BERT embedding
            avg_embedding = np.mean(embeddings, axis=0)
            for i in range(15):
                features[f'bert_content_dim_{i}'] = float(avg_embedding[i])
        else:
            for i in range(15):
                features[f'bert_content_dim_{i}'] = 0.0
        
        return features
    
    def _extract_structural_behavior(self, user_id, user_messages, users, network_graph):
        """Extract structural behavioral features"""
        features = {}
        
        # Basic network structure
        if user_id in network_graph:
            # Network centrality measures
            features['degree_centrality'] = network_graph.degree(user_id) / (len(network_graph.nodes()) - 1)
            
            # Clustering coefficient
            features['clustering_coefficient'] = nx.clustering(network_graph, user_id)
            
            # Connection characteristics
            neighbors = list(network_graph.neighbors(user_id))
            features['num_connections'] = len(neighbors)
            
            # Connection diversity (different user types)
            neighbor_types = [users[neighbor]['user_type'] for neighbor in neighbors if neighbor in users]
            type_diversity = len(set(neighbor_types)) / max(len(neighbor_types), 1)
            features['connection_type_diversity'] = type_diversity
            
            # Average neighbor activity
            neighbor_activities = [users[neighbor]['base_activity'] for neighbor in neighbors if neighbor in users]
            features['avg_neighbor_activity'] = np.mean(neighbor_activities) if neighbor_activities else 0
        else:
            features.update({
                'degree_centrality': 0,
                'clustering_coefficient': 0,
                'num_connections': 0,
                'connection_type_diversity': 0,
                'avg_neighbor_activity': 0
            })
        
        # Messaging behavior to connections
        if user_messages:
            recipients = [msg['recipient_id'] for msg in user_messages if 'recipient_id' in msg]
            
            if recipients:
                # Recipient diversity
                unique_recipients = set(recipients)
                features['recipient_diversity'] = len(unique_recipients) / len(recipients)
                
                # Messages per recipient
                recipient_counts = Counter(recipients)
                features['max_messages_per_recipient'] = max(recipient_counts.values())
                features['avg_messages_per_recipient'] = np.mean(list(recipient_counts.values()))
                
                # Connection vs non-connection messaging
                user_connections = users[user_id]['connections'] if user_id in users else set()
                messages_to_connections = sum(1 for r in recipients if r in user_connections)
                features['connection_messaging_ratio'] = messages_to_connections / len(recipients)
                
                # Spam targeting patterns
                spam_recipients = [msg['recipient_id'] for msg in user_messages if msg['is_spam'] and 'recipient_id' in msg]
                if spam_recipients:
                    spam_to_connections = sum(1 for r in spam_recipients if r in user_connections)
                    features['spam_to_connections_ratio'] = spam_to_connections / len(spam_recipients)
                    
                    # Spam recipient diversity
                    features['spam_recipient_diversity'] = len(set(spam_recipients)) / len(spam_recipients)
                else:
                    features['spam_to_connections_ratio'] = 0
                    features['spam_recipient_diversity'] = 0
                
                # Targeting of influential users
                influential_targets = 0
                for recipient in unique_recipients:
                    if recipient in users and users[recipient]['user_type'] in ['influencer', 'business']:
                        influential_targets += 1
                features['influential_targeting_ratio'] = influential_targets / len(unique_recipients)
            else:
                features.update({
                    'recipient_diversity': 0,
                    'max_messages_per_recipient': 0,
                    'avg_messages_per_recipient': 0,
                    'connection_messaging_ratio': 0,
                    'spam_to_connections_ratio': 0,
                    'spam_recipient_diversity': 0,
                    'influential_targeting_ratio': 0
                })
        else:
            features.update({
                'recipient_diversity': 0,
                'max_messages_per_recipient': 0,
                'avg_messages_per_recipient': 0,
                'connection_messaging_ratio': 0,
                'spam_to_connections_ratio': 0,
                'spam_recipient_diversity': 0,
                'influential_targeting_ratio': 0
            })
        
        # User characteristics affecting structure
        user_data = users[user_id]
        features['user_type_influence'] = 1 if user_data['user_type'] in ['influencer', 'business'] else 0
        features['account_age_normalized'] = min(user_data['account_age_days'] / 2190, 1.0)
        
        return features
    
    def _extract_temporal_behavior(self, user_messages, user_data):
        """Extract temporal behavioral features"""
        features = {}
        
        if not user_messages:
            return {f'temporal_{i}': 0.0 for i in range(20)}
        
        # Basic temporal statistics
        timestamps = [msg['timestamp'] for msg in user_messages]
        timestamps.sort()
        
        features['total_messages'] = len(user_messages)
        
        # Activity rate
        time_span = (timestamps[-1] - timestamps[0]).total_seconds() / 86400  # days
        features['messages_per_day'] = len(user_messages) / max(time_span, 1)
        
        # Time-of-day patterns
        hours = [ts.hour for ts in timestamps]
        features['night_activity_ratio'] = sum(1 for h in hours if h < 6 or h > 22) / len(hours)
        features['business_hours_ratio'] = sum(1 for h in hours if 9 <= h <= 17) / len(hours)
        features['peak_hours_ratio'] = sum(1 for h in hours if 18 <= h <= 21) / len(hours)
        
        # Day-of-week patterns
        weekdays = [ts.weekday() for ts in timestamps]  # 0=Monday, 6=Sunday
        features['weekday_ratio'] = sum(1 for d in weekdays if d < 5) / len(weekdays)
        features['weekend_ratio'] = sum(1 for d in weekdays if d >= 5) / len(weekdays)
        
        # Activity variance
        daily_counts = defaultdict(int)
        for ts in timestamps:
            daily_counts[ts.date()] += 1
        
        daily_activity = list(daily_counts.values())
        features['activity_variance'] = np.var(daily_activity) if len(daily_activity) > 1 else 0
        features['activity_consistency'] = 1 / (1 + features['activity_variance'])  # Inverse of variance
        
        # Burst detection
        if len(timestamps) > 1:
            time_diffs = [(timestamps[i+1] - timestamps[i]).total_seconds() / 3600 for i in range(len(timestamps)-1)]
            features['min_time_between_messages'] = min(time_diffs)
            features['avg_time_between_messages'] = np.mean(time_diffs)
            features['burst_activity_ratio'] = sum(1 for td in time_diffs if td < 1) / len(time_diffs)
        else:
            features.update({
                'min_time_between_messages': 24,
                'avg_time_between_messages': 24,
                'burst_activity_ratio': 0
            })
        
        # Compromise-related temporal analysis
        if user_data.get('compromise_date'):
            compromise_date = user_data['compromise_date']
            
            # Split messages into pre and post compromise
            pre_compromise = [msg for msg in user_messages if msg['timestamp'] < compromise_date]
            post_compromise = [msg for msg in user_messages if msg['timestamp'] >= compromise_date]
            
            features['pre_compromise_messages'] = len(pre_compromise)
            features['post_compromise_messages'] = len(post_compromise)
            
            # Activity change analysis
            if len(pre_compromise) > 0 and len(post_compromise) > 0:
                pre_span = (compromise_date - min(msg['timestamp'] for msg in pre_compromise)).days
                post_span = (max(msg['timestamp'] for msg in post_compromise) - compromise_date).days
                
                pre_rate = len(pre_compromise) / max(pre_span, 1)
                post_rate = len(post_compromise) / max(post_span, 1)
                
                features['activity_change_ratio'] = post_rate / max(pre_rate, 0.1)
                
                # Spam rate change
                pre_spam_rate = sum(1 for msg in pre_compromise if msg['is_spam']) / len(pre_compromise)
                post_spam_rate = sum(1 for msg in post_compromise if msg['is_spam']) / len(post_compromise)
                features['spam_rate_change'] = post_spam_rate - pre_spam_rate
                
                # Timing pattern change
                pre_hours = [msg['timestamp'].hour for msg in pre_compromise]
                post_hours = [msg['timestamp'].hour for msg in post_compromise]
                
                pre_night_ratio = sum(1 for h in pre_hours if h < 6 or h > 22) / len(pre_hours)
                post_night_ratio = sum(1 for h in post_hours if h < 6 or h > 22) / len(post_hours)
                features['night_activity_change'] = post_night_ratio - pre_night_ratio
            else:
                features.update({
                    'activity_change_ratio': 1.0,
                    'spam_rate_change': 0.0,
                    'night_activity_change': 0.0
                })
        else:
            features.update({
                'pre_compromise_messages': len(user_messages),
                'post_compromise_messages': 0,
                'activity_change_ratio': 1.0,
                'spam_rate_change': 0.0,
                'night_activity_change': 0.0
            })
        
        # Spam temporal patterns
        spam_messages = [msg for msg in user_messages if msg['is_spam']]
        if spam_messages:
            spam_timestamps = [msg['timestamp'] for msg in spam_messages]
            spam_hours = [ts.hour for ts in spam_timestamps]
            
            features['spam_night_ratio'] = sum(1 for h in spam_hours if h < 6 or h > 22) / len(spam_hours)
            
            # Spam burst detection
            if len(spam_timestamps) > 1:
                spam_timestamps.sort()
                spam_diffs = [(spam_timestamps[i+1] - spam_timestamps[i]).total_seconds() / 3600 
                             for i in range(len(spam_timestamps)-1)]
                features['spam_burst_ratio'] = sum(1 for td in spam_diffs if td < 2) / len(spam_diffs)
            else:
                features['spam_burst_ratio'] = 0
        else:
            features.update({
                'spam_night_ratio': 0,
                'spam_burst_ratio': 0
            })
        
        return features
    
    def prepare_model_input(self, user_features):
        """Prepare features for your Behavior-Aware GAT model"""
        print("🔧 Preparing three-dimensional model input...")
        
        user_ids = list(user_features.keys())
        labels = [user_features[uid]['label'] for uid in user_ids]
        
        # Extract three feature matrices
        content_features = []
        structural_features = []
        temporal_features = []
        
        for uid in user_ids:
            features = user_features[uid]
            content_features.append(list(features['content_features'].values()))
            structural_features.append(list(features['structural_features'].values()))
            temporal_features.append(list(features['temporal_features'].values()))
        
        # Convert to tensors and normalize
        scaler = StandardScaler()
        
        content_matrix = torch.FloatTensor(scaler.fit_transform(np.array(content_features)))
        structural_matrix = torch.FloatTensor(scaler.fit_transform(np.array(structural_features)))
        temporal_matrix = torch.FloatTensor(scaler.fit_transform(np.array(temporal_features)))
        
        print(f"✅ Prepared three-dimensional features:")
        print(f"   📝 Content: {content_matrix.shape[1]} dimensions")
        print(f"   🌐 Structural: {structural_matrix.shape[1]} dimensions")
        print(f"   ⏰ Temporal: {temporal_matrix.shape[1]} dimensions")
        
        return {
            'user_ids': user_ids,
            'labels': torch.LongTensor(labels),
            'content_features': content_matrix,
            'structural_features': structural_matrix,
            'temporal_features': temporal_matrix
        }

# Usage example
if __name__ == "__main__":
    print("🎯 Three-Dimensional Feature Extractor ready!")
    print("   📝 Content Behavior: What they say and how they say it")
    print("   🌐 Structural Behavior: Who they message and connection patterns")
    print("   ⏰ Temporal Behavior: When and how frequently they message")
