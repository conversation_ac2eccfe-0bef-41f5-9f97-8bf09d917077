"""
Compromised Account Dataset Generator
Focused on detecting accounts that spam their trusted connections
"""

import numpy as np
import pandas as pd
import torch
from datetime import datetime, timedelta
import random
import networkx as nx
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

class CompromisedAccountDatasetGenerator:
    """
    Generate realistic dataset focused on the core problem:
    Detecting compromised accounts that abuse trust relationships to spam connections
    """
    
    def __init__(self, num_users=3000, num_messages=12000, compromise_rate=0.08):
        self.num_users = num_users
        self.num_messages = num_messages
        self.compromise_rate = compromise_rate
        
        # Core insight: Compromised accounts exploit TRUST
        # They spam people who trust them (friends, followers, connections)
        
        # Trust relationship types
        self.trust_relationships = {
            'close_friend': {'trust_level': 0.9, 'spam_effectiveness': 0.8},
            'friend': {'trust_level': 0.7, 'spam_effectiveness': 0.6},
            'acquaintance': {'trust_level': 0.5, 'spam_effectiveness': 0.4},
            'follower': {'trust_level': 0.3, 'spam_effectiveness': 0.3},
            'stranger': {'trust_level': 0.1, 'spam_effectiveness': 0.1}
        }
        
        # Compromise scenarios focused on trust abuse
        self.compromise_scenarios = {
            'account_takeover': {
                'probability': 0.4,
                'behavior': 'immediate_spam_burst',  # Spam trusted connections quickly
                'target_preference': 'close_connections',
                'spam_rate': 0.6,  # 60% of messages to trusted connections are spam
                'detection_difficulty': 'medium'
            },
            'gradual_compromise': {
                'probability': 0.3,
                'behavior': 'slow_increase',  # Gradually increase spam over time
                'target_preference': 'all_connections',
                'spam_rate': 0.3,  # 30% spam rate, harder to detect
                'detection_difficulty': 'hard'
            },
            'credential_stuffing': {
                'probability': 0.2,
                'behavior': 'random_spam',  # Less sophisticated, random targeting
                'target_preference': 'random_connections',
                'spam_rate': 0.5,
                'detection_difficulty': 'easy'
            },
            'social_engineering': {
                'probability': 0.1,
                'behavior': 'targeted_high_value',  # Target specific high-value connections
                'target_preference': 'influential_connections',
                'spam_rate': 0.2,  # Low volume, high sophistication
                'detection_difficulty': 'very_hard'
            }
        }
        
    def generate_trust_network(self):
        """Generate social network with explicit trust relationships"""
        users = {}
        
        # Generate users with trust-building characteristics
        for i in range(self.num_users):
            user_id = f"user_{i}"
            
            # User characteristics that affect trust relationships
            age = np.random.randint(18, 70)
            account_age_days = np.random.randint(30, 2190)  # 1 month to 6 years
            
            # Activity level affects how many connections they have
            activity_level = np.random.choice(['low', 'medium', 'high'], p=[0.3, 0.5, 0.2])
            
            if activity_level == 'low':
                base_connections = np.random.randint(20, 100)
            elif activity_level == 'medium':
                base_connections = np.random.randint(100, 500)
            else:  # high
                base_connections = np.random.randint(500, 2000)
            
            # Trust-building factors
            trustworthiness_score = np.random.uniform(0.3, 0.9)  # How much others trust this user
            social_influence = np.random.uniform(0.1, 0.8)      # How influential they are
            
            # Vulnerability to compromise (realistic factors)
            vulnerability = 0.0
            
            # Age factor (older users more vulnerable to some attacks)
            if age > 55:
                vulnerability += 0.2
            elif age < 25:
                vulnerability += 0.15  # Young users also vulnerable
            
            # Account age (newer accounts more vulnerable)
            if account_age_days < 180:
                vulnerability += 0.15
            
            # Activity level (very high activity = more exposure)
            if activity_level == 'high':
                vulnerability += 0.1
            
            # Trustworthiness paradox: highly trusted users are valuable targets
            if trustworthiness_score > 0.7:
                vulnerability += 0.1
            
            # Random factor
            vulnerability += np.random.uniform(-0.1, 0.1)\n            vulnerability = np.clip(vulnerability, 0, 1)\n            \n            # Determine if compromised\n            is_compromised = vulnerability > (1 - self.compromise_rate * 2)\n            \n            # Select compromise scenario if compromised\n            compromise_scenario = None\n            compromise_date = None\n            if is_compromised:\n                compromise_scenario = np.random.choice(\n                    list(self.compromise_scenarios.keys()),\n                    p=[0.4, 0.3, 0.2, 0.1]\n                )\n                # Compromise happened recently (1-90 days ago)\n                compromise_date = datetime.now() - timedelta(days=np.random.randint(1, 90))\n            \n            users[user_id] = {\n                'user_id': user_id,\n                'age': age,\n                'account_age_days': account_age_days,\n                'activity_level': activity_level,\n                'base_connections': base_connections,\n                'trustworthiness_score': trustworthiness_score,\n                'social_influence': social_influence,\n                'vulnerability': vulnerability,\n                'is_compromised': is_compromised,\n                'compromise_scenario': compromise_scenario,\n                'compromise_date': compromise_date,\n                'trusted_connections': {},  # Will store trust relationships\n                'connection_history': []    # Track when connections were made\n            }\n        \n        # Build trust network\n        trust_graph = self._build_trust_relationships(users)\n        \n        return users, trust_graph\n    \n    def _build_trust_relationships(self, users):\n        \"\"\"Build network with explicit trust levels between users\"\"\"\n        G = nx.Graph()\n        user_list = list(users.keys())\n        G.add_nodes_from(user_list)\n        \n        for user_id, user_data in users.items():\n            num_connections = user_data['base_connections']\n            trustworthiness = user_data['trustworthiness_score']\n            \n            # Select connections based on compatibility and trust factors\n            potential_connections = []\n            \n            for other_id, other_data in users.items():\n                if other_id == user_id:\n                    continue\n                \n                # Calculate connection probability based on compatibility\n                age_compatibility = 1 - abs(user_data['age'] - other_data['age']) / 50\n                activity_compatibility = self._activity_compatibility(\n                    user_data['activity_level'], other_data['activity_level']\n                )\n                \n                # Trust attraction: people connect to trustworthy individuals\n                trust_attraction = other_data['trustworthiness_score']\n                \n                connection_prob = (age_compatibility + activity_compatibility + trust_attraction) / 3\n                potential_connections.append((other_id, connection_prob))\n            \n            # Sort by connection probability\n            potential_connections.sort(key=lambda x: x[1], reverse=True)\n            \n            # Select connections (more trustworthy users get more connections)\n            actual_connections = min(num_connections, len(potential_connections))\n            actual_connections = int(actual_connections * (0.5 + trustworthiness * 0.5))\n            \n            selected_connections = potential_connections[:actual_connections]\n            \n            # Establish trust relationships\n            for connected_user, connection_strength in selected_connections:\n                if not G.has_edge(user_id, connected_user):\n                    # Determine trust level based on connection strength and time\n                    trust_level = self._determine_trust_level(connection_strength, user_data, users[connected_user])\n                    \n                    # Add edge with trust information\n                    G.add_edge(user_id, connected_user, \n                             trust_level=trust_level,\n                             connection_strength=connection_strength,\n                             established_date=datetime.now() - timedelta(days=np.random.randint(1, user_data['account_age_days'])))\n                    \n                    # Store in user's trusted connections\n                    users[user_id]['trusted_connections'][connected_user] = trust_level\n                    users[connected_user]['trusted_connections'][user_id] = trust_level\n        \n        return G\n    \n    def _activity_compatibility(self, activity1, activity2):\n        \"\"\"Calculate compatibility between activity levels\"\"\"\n        activity_map = {'low': 1, 'medium': 2, 'high': 3}\n        diff = abs(activity_map[activity1] - activity_map[activity2])\n        return 1 - (diff / 2)  # 0-1 scale\n    \n    def _determine_trust_level(self, connection_strength, user1, user2):\n        \"\"\"Determine trust level between two users\"\"\"\n        base_trust = connection_strength\n        \n        # Trust builds over time and with trustworthiness\n        trust_bonus = (user1['trustworthiness_score'] + user2['trustworthiness_score']) / 2\n        \n        final_trust = (base_trust + trust_bonus) / 2\n        \n        # Categorize trust level\n        if final_trust > 0.8:\n            return 'close_friend'\n        elif final_trust > 0.6:\n            return 'friend'\n        elif final_trust > 0.4:\n            return 'acquaintance'\n        elif final_trust > 0.2:\n            return 'follower'\n        else:\n            return 'stranger'\n    \n    def generate_trust_abuse_messages(self, users, trust_graph):\n        \"\"\"Generate messages focused on trust relationship abuse\"\"\"\n        messages = []\n        \n        for i in range(self.num_messages):\n            # Select sender (compromised users send more messages)\n            sender_weights = []\n            for user_id, user_data in users.items():\n                if user_data['is_compromised']:\n                    weight = len(user_data['trusted_connections']) * 3  # Compromised users more active\n                else:\n                    weight = len(user_data['trusted_connections'])\n                sender_weights.append(weight)\n            \n            if sum(sender_weights) == 0:\n                continue\n                \n            sender_weights = np.array(sender_weights) / sum(sender_weights)\n            sender_idx = np.random.choice(len(users), p=sender_weights)\n            sender_id = f\"user_{sender_idx}\"\n            sender = users[sender_id]\n            \n            # Select recipient from trusted connections\n            if not sender['trusted_connections']:\n                continue\n                \n            recipient_id, trust_level = self._select_recipient(sender, trust_graph)\n            if not recipient_id:\n                continue\n            \n            # Determine if message is spam based on compromise status and trust abuse\n            is_spam = self._determine_spam_for_trust_abuse(sender, trust_level)\n            \n            # Generate content that exploits trust\n            content = self._generate_trust_exploiting_content(is_spam, trust_level, sender, recipient_id)\n            \n            # Generate timestamp with compromise-specific patterns\n            timestamp = self._generate_compromise_timestamp(sender, is_spam)\n            \n            message = {\n                'message_id': f\"msg_{i}\",\n                'sender_id': sender_id,\n                'recipient_id': recipient_id,\n                'trust_level': trust_level,\n                'content': content,\n                'timestamp': timestamp,\n                'is_spam': is_spam,\n                'sender_compromised': sender['is_compromised'],\n                'compromise_scenario': sender['compromise_scenario'],\n                'trust_abuse_score': self._calculate_trust_abuse_score(sender, trust_level, is_spam)\n            }\n            \n            messages.append(message)\n        \n        return messages\n    \n    def _select_recipient(self, sender, trust_graph):\n        \"\"\"Select recipient based on compromise scenario and trust targeting\"\"\"\n        if not sender['trusted_connections']:\n            return None, None\n        \n        if sender['is_compromised']:\n            scenario = self.compromise_scenarios[sender['compromise_scenario']]\n            target_preference = scenario['target_preference']\n            \n            if target_preference == 'close_connections':\n                # Target highest trust connections\n                high_trust_connections = [\n                    (uid, trust) for uid, trust in sender['trusted_connections'].items()\n                    if trust in ['close_friend', 'friend']\n                ]\n                if high_trust_connections:\n                    recipient_id, trust_level = random.choice(high_trust_connections)\n                    return recipient_id, trust_level\n            \n            elif target_preference == 'influential_connections':\n                # Target connections with high social influence\n                influential_connections = []\n                for uid, trust in sender['trusted_connections'].items():\n                    if users[uid]['social_influence'] > 0.6:\n                        influential_connections.append((uid, trust))\n                if influential_connections:\n                    recipient_id, trust_level = random.choice(influential_connections)\n                    return recipient_id, trust_level\n        \n        # Default: random selection from trusted connections\n        recipient_id = random.choice(list(sender['trusted_connections'].keys()))\n        trust_level = sender['trusted_connections'][recipient_id]\n        return recipient_id, trust_level\n    \n    def _determine_spam_for_trust_abuse(self, sender, trust_level):\n        \"\"\"Determine if message is spam based on trust abuse patterns\"\"\"\n        if not sender['is_compromised']:\n            # Legitimate users rarely spam trusted connections\n            return np.random.random() < 0.02  # 2% false positive rate\n        \n        # Compromised users exploit trust relationships\n        scenario = self.compromise_scenarios[sender['compromise_scenario']]\n        base_spam_rate = scenario['spam_rate']\n        \n        # Higher trust = more effective spam = more likely to be used\n        trust_multiplier = self.trust_relationships[trust_level]['spam_effectiveness']\n        \n        final_spam_rate = base_spam_rate * (0.5 + trust_multiplier * 0.5)\n        \n        return np.random.random() < final_spam_rate\n    \n    def _generate_trust_exploiting_content(self, is_spam, trust_level, sender, recipient_id):\n        \"\"\"Generate content that exploits trust relationships\"\"\"\n        if is_spam:\n            # Spam content exploits trust\n            if trust_level == 'close_friend':\n                templates = [\n                    f\"Hey {recipient_id}, I found this amazing opportunity that I had to share with you!\",\n                    f\"Hi! You know I wouldn't recommend anything I didn't believe in. Check this out!\",\n                    f\"I've been using this for weeks and had to tell you about it!\"\n                ]\n            elif trust_level == 'friend':\n                templates = [\n                    f\"Hi {recipient_id}! Thought you might be interested in this.\",\n                    f\"Found something that might help you. Take a look!\",\n                    f\"This worked great for me, might work for you too!\"\n                ]\n            else:\n                templates = [\n                    f\"Hi! Check out this opportunity.\",\n                    f\"You might find this interesting.\",\n                    f\"Thought I'd share this with you.\"\n                ]\n        else:\n            # Legitimate content between trusted connections\n            if trust_level == 'close_friend':\n                templates = [\n                    f\"Hey {recipient_id}! How are you doing?\",\n                    f\"Thanks for your help yesterday!\",\n                    f\"Hope you're having a great day!\"\n                ]\n            elif trust_level == 'friend':\n                templates = [\n                    f\"Hi {recipient_id}, hope you're well!\",\n                    f\"Saw your post, really interesting!\",\n                    f\"Thanks for connecting!\"\n                ]\n            else:\n                templates = [\n                    f\"Hello {recipient_id}.\",\n                    f\"Thanks for the connection.\",\n                    f\"Hope you're doing well.\"\n                ]\n        \n        return np.random.choice(templates)\n    \n    def _generate_compromise_timestamp(self, sender, is_spam):\n        \"\"\"Generate timestamp based on compromise behavior patterns\"\"\"\n        base_time = datetime.now() - timedelta(hours=np.random.randint(0, 168))\n        \n        if sender['is_compromised'] and is_spam:\n            scenario = sender['compromise_scenario']\n            \n            if scenario == 'account_takeover':\n                # Immediate spam burst after compromise\n                days_since_compromise = (datetime.now() - sender['compromise_date']).days\n                if days_since_compromise < 7:  # First week after compromise\n                    # More activity during compromise period\n                    base_time = sender['compromise_date'] + timedelta(hours=np.random.randint(0, 168))\n            \n            elif scenario == 'gradual_compromise':\n                # Gradually increasing activity\n                days_since_compromise = (datetime.now() - sender['compromise_date']).days\n                if days_since_compromise > 30:  # More recent activity\n                    base_time = datetime.now() - timedelta(hours=np.random.randint(0, 72))\n        \n        return base_time\n    \n    def _calculate_trust_abuse_score(self, sender, trust_level, is_spam):\n        \"\"\"Calculate how much this message abuses trust relationship\"\"\"\n        if not is_spam:\n            return 0.0\n        \n        if not sender['is_compromised']:\n            return 0.1  # Low abuse score for false positives\n        \n        # High trust abuse when compromised accounts spam trusted connections\n        trust_value = self.trust_relationships[trust_level]['trust_level']\n        scenario_severity = {\n            'account_takeover': 0.8,\n            'gradual_compromise': 0.6,\n            'credential_stuffing': 0.4,\n            'social_engineering': 0.9\n        }\n        \n        severity = scenario_severity.get(sender['compromise_scenario'], 0.5)\n        \n        return trust_value * severity\n    \n    def generate_complete_dataset(self):\n        \"\"\"Generate complete dataset focused on trust relationship abuse\"\"\"\n        print(f\"🔬 Generating compromised account dataset with {self.num_users} users...\")\n        print(\"🎯 Focus: Detecting accounts that spam their trusted connections\")\n        \n        # Generate trust network\n        users, trust_graph = self.generate_trust_network()\n        compromised_count = sum(1 for u in users.values() if u['is_compromised'])\n        \n        print(f\"  📊 Generated {len(users)} users ({compromised_count} compromised, {compromised_count/len(users)*100:.1f}%)\")\n        print(f\"  🤝 Generated trust network with {trust_graph.number_of_edges()} trust relationships\")\n        \n        # Generate trust-abuse messages\n        messages = self.generate_trust_abuse_messages(users, trust_graph)\n        spam_count = sum(1 for m in messages if m['is_spam'])\n        trust_abuse_count = sum(1 for m in messages if m['trust_abuse_score'] > 0.5)\n        \n        print(f\"  💬 Generated {len(messages)} messages ({spam_count} spam, {trust_abuse_count} high trust abuse)\")\n        \n        # Calculate trust abuse statistics\n        trust_distribution = {}\n        for msg in messages:\n            trust_level = msg['trust_level']\n            trust_distribution[trust_level] = trust_distribution.get(trust_level, 0) + 1\n        \n        print(f\"  🎯 Trust level distribution: {trust_distribution}\")\n        \n        return {\n            'users': users,\n            'messages': messages,\n            'trust_graph': trust_graph,\n            'statistics': {\n                'num_users': len(users),\n                'num_compromised': compromised_count,\n                'compromise_rate': compromised_count / len(users),\n                'num_messages': len(messages),\n                'num_spam': spam_count,\n                'spam_rate': spam_count / len(messages),\n                'trust_abuse_messages': trust_abuse_count,\n                'trust_abuse_rate': trust_abuse_count / len(messages),\n                'trust_distribution': trust_distribution\n            }\n        }\n\n# Usage example\nif __name__ == \"__main__\":\n    generator = CompromisedAccountDatasetGenerator(num_users=1000, num_messages=4000)\n    dataset = generator.generate_complete_dataset()\n    \n    print(\"\\n🎯 Example Trust Abuse Messages:\")\n    trust_abuse_messages = [msg for msg in dataset['messages'] if msg['trust_abuse_score'] > 0.5]\n    for i, msg in enumerate(trust_abuse_messages[:3]):\n        print(f\"  {i+1}. {msg['sender_id']} → {msg['recipient_id']} (Trust: {msg['trust_level']})\")\n        print(f\"     Content: {msg['content']}\")\n        print(f\"     Trust Abuse Score: {msg['trust_abuse_score']:.2f}\")
